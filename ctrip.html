﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA Order Converter - 携程专车订单处理</title>
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #2196f3; /* 携程蓝色 */
            --primary-hover: #1976d2;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --success-color: #10b981;
            --success-hover: #059669;
            --danger-color: #ef4444;
            --danger-hover: #dc2626;
            --warning-color: #f59e0b;
            --warning-hover: #d97706;
            --border-color: #e5e7eb;
            --bg-color: #f9fafb;
            --text-color: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
        }
        
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            line-height: 1.5;
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
            transition: var(--transition);
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }
        
        .card:hover {
            box-shadow: var(--shadow);
        }
        
        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(59, 130, 246, 0.03);
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .form-input, textarea, select {
            width: 100%;
            padding: 0.625rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: #fff;
            color: var(--text-color);
            font-size: 1rem;
            line-height: 1.5;
            transition: var(--transition);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .form-input:hover, textarea:hover, select:hover {
            border-color: var(--primary-color);
        }
        
        .form-input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }
        
        .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
            background-color: var(--bg-color);
            cursor: not-allowed;
            opacity: 0.75;
            box-shadow: none;
        }
        
        textarea {
            min-height: 150px;
            resize: vertical;
            line-height: 1.6;
        }
        
        /* 移动端文本区域样式 */
        @media (max-width: 767px) {
            textarea {
                min-height: 120px;
            }
        }
        
        .btn {
            display: inline-flex;
            font-weight: 500;
            justify-content: center;
            align-items: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.625rem 1.25rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: var(--radius);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        
        .btn-primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-gray {
            color: #fff;
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-gray:hover {
            background-color: var(--secondary-hover);
            border-color: var(--secondary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-gray:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-green {
            color: #fff;
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-green:hover {
            background-color: var(--success-hover);
            border-color: var(--success-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-green:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        /* 移动端按钮组样式 */
        @media (max-width: 767px) {
            .button-group {
                flex-direction: column;
            }
            
            .button-group .btn {
                width: 100%;
                margin-bottom: 0.5rem;
                min-height: 44px; /* 确保触摸目标足够大 */
            }
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .hidden {
            display: none;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        /* 移动端适配 */
        @media (max-width: 767px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .header-content a {
                margin-top: 0.5rem;
            }
            
            h1 {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
            
            .logo-container img {
                height: 1.5rem;
            }
        }
        
        .col-span-2 {
            grid-column: span 1;
        }
        
        @media (min-width: 768px) {
            .col-span-2 {
                grid-column: span 2;
            }
        }
        
        footer {
            margin-top: auto;
            padding: 1rem 0;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            /* 兼容Edge浏览器 */
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        footer .container {
            /* 兼容旧版Edge浏览器，使用替代方案实现文本居中 */
            display: flex;
            justify-content: center;
        }
        
        .notification {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            background-color: var(--success-color);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow-md);
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            z-index: 50;
            max-width: 350px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .notification::before {
            content: '✔';
            margin-right: 0.5rem;
            font-weight: bold;
        }
        
        /* 移动端通知样式 */
        @media (max-width: 767px) {
            .notification {
                bottom: 1rem;
                right: 1rem;
                left: 1rem;
                font-size: 0.875rem;
                padding: 0.75rem 1rem;
                max-width: none;
                border-radius: var(--radius-sm);
            }
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .order-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            transition: var(--transition);
            background-color: white;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }
        
        .order-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-item:hover::after {
            opacity: 1;
        }
        
        /* 多订单样式 */
        .order-card {
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            padding: 1.25rem;
            transition: var(--transition);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }
        
        .order-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-card:hover::after {
            opacity: 1;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .order-content p {
            margin: 0.3rem 0;
            font-size: 0.9rem;
        }
        
        /* 移动端多订单样式 */
        @media (max-width: 767px) {
            #orders-list {
                grid-template-columns: 1fr !important;
                gap: 0.75rem !important;
                padding: 0.75rem !important;
            }
            
            .order-item {
                padding: 0.75rem;
            }
            
            .order-item-mobile .order-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .order-item-mobile .order-header button {
                margin-top: 0.5rem;
                width: 100%;
            }
            
            .order-item-mobile .order-content {
                display: flex;
                flex-direction: column;
            }
            
            /* 表单元素在移动端的样式 */
            .form-input, textarea, select {
                font-size: 16px; /* 防止iOS自动缩放 */
                padding: 0.625rem;
            }
            
            /* 移动端卡片样式优化 */
            .card {
                border-radius: 0.375rem;
                margin-bottom: 1rem;
            }
            
            .card-header {
                padding: 0.875rem 1rem;
            }
            
            .card-title {
                font-size: 1rem;
            }
            
            /* 移动端按钮优化 */
            .btn {
                min-height: 44px; /* 确保触摸目标足够大 */
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        
        /* 等待动画样式 */
        #api-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.85);
            -webkit-backdrop-filter: blur(3px);
            backdrop-filter: blur(3px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        #api-loader.show {
            opacity: 1;
        }
        
        #api-loader .spinner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            padding: 1.5rem;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
        }
        
        #api-loader .spinner {
            border: 3px solid rgba(59, 130, 246, 0.2);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        #api-loader .spinner-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* 添加深色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #111827;
                --text-color: #f9fafb;
                --text-secondary: #9ca3af;
                --border-color: #374151;
            }
            
            body {
                background-color: var(--bg-color);
                color: var(--text-color);
            }
            
            header, footer, .card, .order-item, .order-card {
                background-color: #1f2937;
                border-color: var(--border-color);
            }
            
            .form-input, textarea, select {
                background-color: #111827;
                color: var(--text-color);
                border-color: var(--border-color);
            }
            
            .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
                background-color: rgba(17, 24, 39, 0.7);
            }
            
            #api-loader {
                background-color: rgba(17, 24, 39, 0.85);
            }
            
            #api-loader .spinner-container {
                background-color: #1f2937;
            }
            
            /* 深色模式下的特殊优化 */
            .btn {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            }
            
            .notification {
                background-color: #065f46; /* 深色模式下的成功颜色 */
            }
            
            .order-item::after, .order-card::after {
                background-color: #3b82f6; /* 保持主色调可见性 */
                opacity: 0.2;
            }
            
            .order-item:hover::after, .order-card:hover::after {
                opacity: 1;
            }
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }

        /* 通知类型样式 */
        .notification.success { background-color: var(--success-color); }
        .notification.error   { background-color: var(--danger-color); }
        .notification.warning { background-color: var(--warning-color); color: var(--text-color); } /* 警告用亮色背景配深色文字 */
        .notification.info    { background-color: var(--secondary-color); }

        /* 为不同类型添加不同图标 (可选) */
        .notification.success::before { content: '✔'; }
        .notification.error::before   { content: '✖'; }
        .notification.warning::before { content: '⚠'; font-size: 1.1em; }
        .notification.info::before    { content: 'ℹ'; }

        /* 警告类型的深色模式适配 */
        @media (prefers-color-scheme: dark) {
            .notification.warning { background-color: var(--warning-color); color: #111827; } /* 深色模式下警告仍用深色文字 */
        }

        /* 编辑状态高亮 */
        .card.editing {
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.4); /* 使用 warning 色的外发光 */
        }
    </style>
    <style>
        /* 设备特定样式 */
        /* 移动设备优化 */
        html.mobile .btn, html.tablet .btn {
            min-height: 44px; /* 确保触摸目标足够大 */
            padding: 0.625rem 1rem;
        }
        
        html.mobile .form-input, html.mobile textarea, html.mobile select,
        html.tablet .form-input, html.tablet textarea, html.tablet select {
            font-size: 16px; /* 防止iOS自动缩放 */
            padding: 0.625rem;
        }
        
        html.mobile #orders-list, html.tablet #orders-list {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }
        
        html.mobile .order-item, html.tablet .order-item {
            padding: 0.75rem;
        }
        
        html.mobile .notification, html.tablet .notification {
            left: 1rem;
            right: 1rem;
            bottom: 1rem;
            max-width: none;
        }
        
        /* Edge浏览器特定优化 */
        html.edge .btn {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        html.edge body {
            text-rendering: optimizeLegibility;
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="notification" id="notification"></div>
    
    <header>
        <div class="container header-content">
            <div class="logo-container">
                <img src="images/logo.svg" alt="Logo">
                <h1 data-i18n="app-title">携程专车订单处理</h1>
            </div>
            <a href="index.html" class="btn btn-gray" data-i18n="back-to-home">返回首页</a>
        </div>
    </header>

    <main class="container">
        <div class="grid">
            <!-- 输入部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="携程专车-original-order">携程专车原始订单</h2>
                    <div class="lang-selector">
                        <select id="language-selector" class="form-input" style="width: auto; min-width: 120px;" aria-label="选择语言 Select language">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                            <option value="jp">日本語</option>
                            <option value="ko">한국어</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="raw-order" class="form-label" data-i18n="original-order-data">原始订单数据</label>
                        <textarea id="raw-order" data-i18n-placeholder="paste-携程专车-order-data" placeholder="粘贴携程专车原始订单数据..."></textarea>
                    </div>
                    <div class="button-group">
                        <button id="convert-btn" class="btn btn-primary flex-1" data-i18n="process-order">处理订单</button>
                        <button id="reset-btn" class="btn btn-gray" data-i18n="reset">重置</button>
                        <button type="button" id="cancel-edit-btn" class="btn btn-secondary flex-1 hidden" data-i18n="cancel-edit">取消编辑</button>
                    </div>
                </div>
            </div>

            <!-- 输出部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="standardized-order">标准化订单</h2>
                    <button id="copy-output" class="btn btn-gray" data-i18n="copy-output">复制</button>
                </div>
                <div class="card-body">
                    <form id="order-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="ota" class="form-label">OTA平台</label>
                                <input type="text" id="ota" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="ota-reference" class="form-label">OTA订单号</label>
                                <input type="text" id="ota-reference" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="price" class="form-label">价格</label>
                                <input type="text" id="price" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="name" class="form-label">乘客姓名</label>
                                <input type="text" id="name" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">电话</label>
                                <input type="text" id="phone" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" id="email" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="flight-number" class="form-label">航班号</label>
                                <input type="text" id="flight-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-datetime" class="form-label">接机时间</label>
                                <input type="text" id="pickup-datetime" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-address" class="form-label">接机地址</label>
                                <input type="text" id="pickup-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="dropoff-address" class="form-label">送机地址</label>
                                <input type="text" id="dropoff-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="car-type" class="form-label">车型</label>
                                <input type="text" id="car-type" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="passenger-number" class="form-label">乘客人数</label>
                                <input type="number" id="passenger-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="luggage-number" class="form-label">行李数量</label>
                                <input type="number" id="luggage-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="language" class="form-label">语言</label>
                                <input type="text" id="language" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="category" class="form-label">类别</label>
                                <select id="category" class="form-input" disabled>
                                    <option value="airport">机场接送</option>
                                    <option value="charter">包车</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="subcategory" class="form-label">子类别</label>
                                <select id="subcategory" class="form-input" disabled>
                                    <option value="pickup">接机</option>
                                    <option value="dropoff">送机</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driving-region" class="form-label">驾驶区域</label>
                                <select id="driving-region" class="form-input" disabled>
                                    <option value="kl">吉隆坡</option>
                                    <option value="penang">槟城</option>
                                    <option value="sg">新加坡</option>
                                    <option value="jb">新山</option>
                                    <option value="sabah">沙巴</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driver-count" class="form-label">司机数量</label>
                                <input type="number" id="driver-count" class="form-input" value="1" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="remark" class="form-label">备注</label>
                            <textarea id="remark" rows="2" class="form-input" readonly></textarea>
                        </div>
                        <div class="button-group">
                            <button type="button" id="edit-btn" class="btn btn-gray flex-1" data-i18n="enable-edit">编辑</button>
                            <button type="button" id="save-btn" class="btn btn-green flex-1 hidden" data-i18n="save-changes">保存</button>
                            <button type="button" id="cancel-edit-btn" class="btn btn-secondary flex-1 hidden" data-i18n="cancel-edit">取消编辑</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 多订单结果容器 -->
        <div id="multi-orders-container" class="card hidden">
            <div class="card-header">
                <h2 class="card-title" data-i18n="multiple-orders-detected">已识别到的多个订单</h2>
            </div>
            <div id="orders-list" class="card-body" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1rem; padding: 1rem;">
                <!-- 订单条目将在这里动态添加 -->
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            Built with <a href="https://flowith.net" target="_blank" rel="noopener" style="color: #3b82f6; text-decoration: none;">Flowith Oracle</a>.
        </div>
    </footer>

    <script>
        // 统一识别规则数据块
        const orderRecognitionRules = {
            otaRules: {
                "ctrip": {
                    id: 1,
                    provider: 'Ctrip',
                    fieldMappings: [
                        { field: 'vehicle-type', pattern: '接单车型：(.+?)(?=\\n|$)' },
                        { field: 'name', pattern: '乘客姓名：(.+?)(?=\\n|$)' },
                        { field: 'english-name', pattern: '乘客英文名：(.+?)(?=\\n|$)' },
                        { field: 'phone', pattern: '乘客电话：(.+?)(?=\\n|$)' },
                        { field: 'overseas-phone', pattern: '乘客境外电话：(.+?)(?=\\n|$)' },
                        { field: 'extra-contact', pattern: '更多联系方式：(.+?)(?=\\n|$)' },
                        { field: 'pickup-datetime', pattern: '用车时间：(.+?)(?=\\n|$)' },
                        { field: 'adult-count', pattern: '成人数：(.+?)(?=\\n|$)' },
                        { field: 'child-count', pattern: '儿童数：(.+?)(?=\\n|$)' },
                        { field: 'luggage-number', pattern: '行李数：(.+?)(?=\\n|$)' },
                        { field: 'flight-number', pattern: '航班号：(.+?)(?=\\n|$)' },
                        { field: 'flight-landing-time', pattern: '航班落地时间：(.+?)(?=\\n|$)' },
                        { field: 'service-type', pattern: '服务类型：(.+?)(?=\\n|$)' },
                        { field: 'pickup-address', pattern: '上车点：(.+?)(?=\\n|$)' },
                        { field: 'dropoff-address', pattern: '下车点：(.+?)(?=\\n|$)' },
                        { field: 'order-remark', pattern: '订单备注：(.+?)(?=\\n|$)' },
                        { field: 'add-service', pattern: '附加服务：(.+?)(?=\\n|$)' },
                        { field: 'ota-reference', pattern: '订单号：(.+?)(?=\\n|$)' },
                        { field: 'price', pattern: '订单金额：(.+?)(?=\\n|$)' }
                    ]
                }
            },
            vehicleStandard: {
                '紧凑五座': 'Compact 5 Seater',
                '紧凑5座': 'Compact 5 Seater',
                '经济五座': 'Economy 5 Seater',
                '经济5座': 'Economy 5 Seater',
                '舒适五座': 'Comfort 5 Seater',
                '舒适5座': 'Comfort 5 Seater',
                '舒适七座': 'Comfort 7 Seater',
                '舒适7座': 'Comfort 7 Seater',
                '商务七座': 'Business 7 Seater',
                '商务7座': 'Business 7 Seater',
                '豪华七座': 'Luxury 7 Seater',
                '豪华7座': 'Luxury 7 Seater',
                '商务九座': 'Business 9 Seater',
                '商务9座': 'Business 9 Seater',
                '十五座中巴': '15 Seater Minibus',
                '15座中巴': '15 Seater Minibus'
            },
            orderTypeMapping: {
                '接机': 'pickup',
                '送机': 'dropoff',
                '日租': 'charter'
            },
            airportKeywords: [
                '机场', 'airport', '航站楼', 'terminal', '候机楼',
                'kuala lumpur international airport', 'klia', 'klia2',
                '吉隆坡国际机场', '吉隆坡机场', '槟城机场', 'penang airport',
                '新山机场', 'johor airport', 'senai airport', '亚庇机场',
                'kota kinabalu airport', 'bki', 'kkia'
            ],
            // 机场本地翻译映射表
            airportTranslations: {
                // 吉隆坡及周边机场
                '吉隆坡国际机场': 'Kuala Lumpur International Airport',
                '吉隆坡机场': 'Kuala Lumpur International Airport',
                '吉隆坡国际机场1号航站楼': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场2号航站楼': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场t1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场t2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场T2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡T2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIA': 'Kuala Lumpur International Airport',
                'KLIA1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIA2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIAT1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIAT2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIA-T1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIA-T2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡廉价航空': 'Kuala Lumpur Low Cost Carrier Terminal',
                '吉隆坡国际机场1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场2': 'Kuala Lumpur International Airport Terminal 2',
                
                // 新加坡机场
                '新加坡樟宜机场': 'Singapore Changi Airport',
                '新加坡机场': 'Singapore Changi Airport',
                '樟宜机场': 'Singapore Changi Airport',
                '樟宜国际机场': 'Singapore Changi Airport',
                '樟宜机场T1': 'Singapore Changi Airport Terminal 1',
                '樟宜机场T2': 'Singapore Changi Airport Terminal 2',
                '樟宜机场T3': 'Singapore Changi Airport Terminal 3',
                '樟宜机场T4': 'Singapore Changi Airport Terminal 4',
                '新加坡T1': 'Singapore Changi Airport Terminal 1',
                '新加坡T2': 'Singapore Changi Airport Terminal 2',
                '新加坡T3': 'Singapore Changi Airport Terminal 3',
                '新加坡T4': 'Singapore Changi Airport Terminal 4',
                
                // 沙巴及周边机场
                '亚庇机场': 'Kota Kinabalu International Airport',
                '亚庇国际机场': 'Kota Kinabalu International Airport',
                '沙巴机场': 'Kota Kinabalu International Airport',
                '哥打京那巴鲁机场': 'Kota Kinabalu International Airport',
                '哥打京那巴鲁国际机场': 'Kota Kinabalu International Airport',
                '斗湖机场': 'Tawau Airport',
                '仙本那机场': 'Semporna Airport',
                '山打根机场': 'Sandakan Airport',
                
                // 其他马来西亚机场
                '槟城机场': 'Penang International Airport',
                '槟城国际机场': 'Penang International Airport',
                '新山机场': 'Senai International Airport',
                '柔佛机场': 'Johor Bahru International Airport',
                '兰卡威机场': 'Langkawi International Airport',
                '亚罗士打机场': 'Alor Setar Airport',
                '古晋机场': 'Kuching International Airport',
                '马六甲机场': 'Malacca International Airport',
                '哥打巴鲁机场': 'Kota Bharu Airport',
                
                // Subang Airport (新增)
                '梳邦机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                'Subang机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                'SUBANG机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                '苏邦机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                '苏邦国际机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                '梳邦国际机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                '梳邦飞机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)',
                '雪邦机场': 'Sultan Abdul Aziz Shah Airport (Subang Airport)'
            }
        };

        // 订单处理模块 - 封装所有与订单处理相关的函数
        const OrderProcessor = {
            /**
             * 预处理原始订单文本，移除无用信息，统一格式
             * @param {string} rawText 原始订单文本
             * @returns {string} 预处理后的文本
             */
            preprocessText: function(rawText) {
                if (!rawText) return '';
                
                // 1. 去除多余空格和换行
                let processedText = rawText.replace(/\r\n/g, '\n')
                    .replace(/\s+/g, ' ')
                    .replace(/\n\s+/g, '\n')
                    .trim();
                
                // 2. 重新添加合理的换行，便于后续处理
                processedText = processedText.replace(/([：:]\s*)/g, '$1')
                    .replace(/(\d+)\s*[-—]\s*(\d+)/g, '$1-$2');
                    
                console.log('预处理后文本:', processedText);
                return processedText;
            },
            
            /**
             * 拆分订单 - 处理可能的多订单情况
             * @param {string} orderText 预处理后的订单文本
             * @returns {Array<string>} 拆分后的订单文本数组
             */
            splitOrders: function(orderText) {
                // 搜索常见的订单分隔标记
                // 对于携程专车，可以使用"订单号："作为分隔符，假设每个订单都有这个字段
                const orderParts = orderText.split(/(?=订单号：)/g);
                
                // 过滤掉空订单
                const validOrders = orderParts.filter(part => part.trim().length > 0);
                
                if (validOrders.length === 0) {
                    return [orderText]; // 如果没有识别到分隔符，返回原文本作为单个订单
                }
                
                // 这里我们检查第一部分是否包含订单号，如果不是，可能需要特殊处理
                if (!validOrders[0].includes('订单号：') && orderParts[0].trim().length > 0) {
                    // 第一部分没有订单号标记，但有内容，可能是信息的一部分
                    if (validOrders.length > 1) {
                        // 如果有多个部分，尝试将第一部分合并到第二部分
                        validOrders[1] = orderParts[0] + validOrders[1];
                        return validOrders.slice(1);
                    }
                }
                
                return validOrders;
            },
            
            /**
             * 提取单个订单数据
             * @param {string} orderText 单个订单的文本
             * @returns {Object} 提取的订单数据
             */
            extractOrderData: function(orderText) {
                // 1. 初始化订单数据对象，设置默认值
                const orderData = {
                    ota: '携程专车',
                    language: 'Chinese',
                    driver: 1,
                    category: 'airport', // 默认为机场订单
                    subcategory: 'pickup' // 默认为接机
                };
                
                // 2. 根据字段映射规则提取信息
                const fieldMappings = orderRecognitionRules.otaRules.ctrip.fieldMappings;
                
                for (const mapping of fieldMappings) {
                    try {
                        const regex = new RegExp(mapping.pattern, 'i');
                        const match = orderText.match(regex);
                        
                        if (match && match[1]) {
                            const fieldId = mapping.field;
                            orderData[fieldId] = match[1].trim();
                        }
                    } catch (e) {
                        console.error(`提取字段 ${mapping.field} 时出错:`, e);
                    }
                }
                
                // 3. 处理服务类型 - 根据服务类型确定category和subcategory
                if (orderData['service-type']) {
                    const serviceType = orderData['service-type'].toLowerCase();
                    
                    if (serviceType.includes('接机')) {
                        orderData.category = 'airport';
                        orderData.subcategory = 'pickup';
                    } else if (serviceType.includes('送机')) {
                        orderData.category = 'airport';
                        orderData.subcategory = 'dropoff';
                    } else if (serviceType.includes('日租') || serviceType.includes('包车')) {
                        orderData.category = 'charter';
                        orderData.subcategory = 'charter';
                    } else if (serviceType.includes('接')) {
                        // 一般的接送服务，不是机场服务
                        orderData.category = 'charter';
                        orderData.subcategory = 'pickup';
                    } else if (serviceType.includes('送')) {
                        orderData.category = 'charter';
                        orderData.subcategory = 'dropoff';
                    }
                }

                // 4. 处理乘客人数 - 合并成人和儿童数
                if (orderData['adult-count'] || orderData['child-count']) {
                    const adultCount = parseInt(orderData['adult-count'] || '0');
                    const childCount = parseInt(orderData['child-count'] || '0');
                    orderData['passenger-number'] = (adultCount + childCount).toString();
                }
                
                // 5. 处理行李数
                if (orderData['luggage-number'] && orderData['luggage-number'].trim()) {
                    // 行李数已设置
                } else {
                    // 如果没有行李数信息，根据乘客数估算
                    const passengerCount = parseInt(orderData['passenger-number'] || '1');
                    orderData['luggage-number'] = Math.max(1, Math.min(passengerCount, 4)).toString();
                }
                
                // 6. 处理车型
                if (orderData['vehicle-type']) {
                    const vehicleType = orderData['vehicle-type'].trim();
                    // 尝试映射到标准车型
                    for (const [key, value] of Object.entries(orderRecognitionRules.vehicleStandard)) {
                        if (vehicleType.includes(key)) {
                            orderData['vehicle-type'] = value;
                            break;
                        }
                    }
                }
                
                // 7. 处理接送时间和航班时间
                // 用车时间：2025-04-15 15:40:00 格式转换为 15-04-2025 15:40
                if (orderData['pickup-datetime']) {
                    const dateTimeStr = orderData['pickup-datetime'];
                    const dateTimeMatch = dateTimeStr.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})?/);
                    
                    if (dateTimeMatch) {
                        const [_, year, month, day, hour, minute] = dateTimeMatch;
                        orderData['pickup-datetime'] = `${day}-${month}-${year} ${hour}:${minute}`;
                    }
                }
                
                // 8. 处理航班落地时间，如果有的话
                if (orderData['flight-landing-time']) {
                    const landingTimeStr = orderData['flight-landing-time'];
                    const landingTimeMatch = landingTimeStr.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})?/);
                    
                    if (landingTimeMatch) {
                        const [_, year, month, day, hour, minute] = landingTimeMatch;
                        // 添加为处理后的字段，保留原始字段
                        orderData['flight-landing-datetime'] = `${day}-${month}-${year} ${hour}:${minute}`;
                    }
                }
                
                // 9. 处理备注和附加服务 - 合并到remark字段
                // 不管原始订单数据中是否有备注，都将remark设为固定值
                orderData.remark = "要用 携程司导端 工作。 this job need use china app";
                
                // 10. 处理驾驶区域 - 根据地址推断
                // 默认为KL
                orderData['driving-region'] = 'kl';
                
                // 分析地址识别驾驶区域
                const addressesToCheck = [
                    orderData['pickup-address'] || '',
                    orderData['dropoff-address'] || ''
                ].join(' ').toLowerCase();
                
                if (addressesToCheck.includes('penang') || addressesToCheck.includes('槟城') || 
                    addressesToCheck.includes('槟榔屿') || addressesToCheck.includes('pg')) {
                    orderData['driving-region'] = 'penang';
                } else if (addressesToCheck.includes('johor') || addressesToCheck.includes('新山') || 
                    addressesToCheck.includes('柔佛') || addressesToCheck.includes('jb')) {
                    orderData['driving-region'] = 'jb';
                } else if (addressesToCheck.includes('kota kinabalu') || addressesToCheck.includes('亚庇') || 
                    addressesToCheck.includes('沙巴') || addressesToCheck.includes('sabah')) {
                    orderData['driving-region'] = 'kk';
                } else if (addressesToCheck.includes('singapore') || addressesToCheck.includes('新加坡') || 
                    addressesToCheck.includes('狮城') || addressesToCheck.includes('sg')) {
                    orderData['driving-region'] = 'sg';
                }
                
                return orderData;
            },
            
            /**
             * 处理地址信息 - 根据订单类型自动推断并纠正地址
             * @param {Object} orderData 订单数据
             * @returns {Object} 处理后的订单数据
             */
            processAddress: function(orderData) {
                if (!orderData) return orderData;
                
                const category = orderData.category || '';
                const subcategory = orderData.subcategory || '';
                
                // 判断地址是否包含机场关键词
                const isAirportAddress = (address) => {
                    if (!address) return false;
                    const lowerAddress = address.toLowerCase();
                    return orderRecognitionRules.airportKeywords.some(keyword => 
                        lowerAddress.includes(keyword.toLowerCase()));
                };
                
                // 机场地址翻译函数
                const translateAirportAddress = (address) => {
                    if (!address) return address;
                    
                    // 先检查是否已经是英文机场地址
                    if (/airport|terminal|international/i.test(address)) {
                        console.log(`地址已包含英文机场关键词，跳过翻译: ${address}`);
                        return address;
                    }
                    
                    // 尝试在翻译映射中查找完全匹配
                    for (const [key, value] of Object.entries(orderRecognitionRules.airportTranslations)) {
                        if (address.includes(key)) {
                            const translatedAddress = address.replace(key, value);
                            console.log(`机场地址翻译: ${address} -> ${translatedAddress}`);
                            return translatedAddress;
                        }
                    }
                    
                    // 模糊匹配功能 - 计算字符串相似度
                    const calculateSimilarity = (str1, str2) => {
                        // 忽略大小写和空格进行比较
                        const normalize = s => s.toLowerCase().replace(/\s+/g, '');
                        const s1 = normalize(str1);
                        const s2 = normalize(str2);
                        
                        if (s1 === s2) return 100; // 完全匹配
                        if (s1.length === 0 || s2.length === 0) return 0;
                        
                        // Levenshtein距离算法
                        const len1 = s1.length;
                        const len2 = s2.length;
                        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
                        
                        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
                        for (let j = 0; j <= len2; j++) matrix[0][j] = j;
                        
                        for (let i = 1; i <= len1; i++) {
                            for (let j = 1; j <= len2; j++) {
                                const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
                                matrix[i][j] = Math.min(
                                    matrix[i - 1][j] + 1, // 删除
                                    matrix[i][j - 1] + 1, // 插入
                                    matrix[i - 1][j - 1] + cost // 替换或保持
                                );
                            }
                        }
                        
                        // 计算相似度百分比
                        const maxLen = Math.max(len1, len2);
                        const similarity = ((maxLen - matrix[len1][len2]) / maxLen) * 100;
                        return similarity;
                    };
                    
                    // 尝试进行模糊匹配
                    let bestMatch = null;
                    let bestSimilarity = 0;
                    const similarityThreshold = 70; // 相似度阈值，70%以上认为匹配
                    
                    // 将地址分割成可能的机场部分
                    const addressParts = address.split(/[,，、\s]+/);
                    
                    // 遍历地址的各个部分，与机场关键词进行相似度比较
                    for (const addressPart of addressParts) {
                        // 只处理可能是机场的部分（包含"机场"、"航站"等关键词）
                        if (addressPart.includes('机场') || addressPart.includes('航站') || 
                            addressPart.includes('国际') || /sub[au]ng/i.test(addressPart)) {
                            
                            for (const [key, value] of Object.entries(orderRecognitionRules.airportTranslations)) {
                                const similarity = calculateSimilarity(addressPart, key);
                                
                                if (similarity > similarityThreshold && similarity > bestSimilarity) {
                                    bestSimilarity = similarity;
                                    bestMatch = { key, value };
                                }
                            }
                        }
                    }
                    
                    if (bestMatch) {
                        const translatedAddress = address.replace(new RegExp(bestMatch.key, 'i'), bestMatch.value);
                        console.log(`机场地址模糊匹配(${bestSimilarity.toFixed(2)}%): ${address} -> ${translatedAddress}`);
                        return translatedAddress;
                    }
                    
                    // 如果没有完全匹配，尝试通过关键词识别城市和机场
                    const cityMapping = {
                        '吉隆坡': 'Kuala Lumpur',
                        '新加坡': 'Singapore',
                        '亚庇': 'Kota Kinabalu',
                        '沙巴': 'Sabah',
                        '槟城': 'Penang',
                        '新山': 'Johor Bahru',
                        '仙本那': 'Semporna',
                        '兰卡威': 'Langkawi',
                        '古晋': 'Kuching',
                        '马六甲': 'Malacca',
                        '哥打巴鲁': 'Kota Bharu',
                        '梳邦': 'Subang',
                        '苏邦': 'Subang',
                        '雪邦': 'Subang'
                    };
                    
                    // 尝试提取城市名
                    for (const [city, englishCity] of Object.entries(cityMapping)) {
                        if (address.includes(city)) {
                            // 检查是否包含机场关键词
                            if (address.includes('机场') || address.includes('航站')) {
                                // Subang机场特殊处理
                                if (city === '梳邦' || city === '苏邦' || city === '雪邦') {
                                    const translatedAddress = 'Sultan Abdul Aziz Shah Airport (Subang Airport)';
                                    console.log(`根据城市名构建机场地址(Subang特例): ${address} -> ${translatedAddress}`);
                                    return translatedAddress;
                                } else {
                                    const translatedAddress = `${englishCity} International Airport`;
                                    console.log(`根据城市名构建机场地址: ${address} -> ${translatedAddress}`);
                                    return translatedAddress;
                                }
                            }
                        }
                    }
                    
                    // 未找到匹配项，保持原地址
                    return address;
                };
                
                // 1. 处理接机订单 - pickup地址应为机场，dropoff地址应为酒店/目的地
                if (category === 'airport' && subcategory === 'pickup') {
                    // 检查pickup地址，如果不像是机场地址，尝试与dropoff交换
                    if (!isAirportAddress(orderData['pickup-address']) && isAirportAddress(orderData['dropoff-address'])) {
                        // 交换地址
                        const temp = orderData['pickup-address'];
                        orderData['pickup-address'] = orderData['dropoff-address'];
                        orderData['dropoff-address'] = temp;
                        console.log('接机订单地址已交换');
                    }
                    // 确保接机订单的pickup地址是机场
                    if (!isAirportAddress(orderData['pickup-address'])) {
                        // 如果还不是机场地址，尝试从航班号和服务类型推断
                        if (orderData['flight-number']) {
                            // 有航班号，假设是KLIA
                            orderData['pickup-address'] = '吉隆坡国际机场(KLIA Terminal 1)';
                            console.log('接机订单自动设置pickup地址为KLIA');
                        }
                    }
                    
                    // 对机场地址进行翻译
                    if (isAirportAddress(orderData['pickup-address'])) {
                        orderData['pickup-address'] = translateAirportAddress(orderData['pickup-address']);
                    }
                }
                
                // 2. 处理送机订单 - pickup地址应为酒店/出发地，dropoff地址应为机场
                if (category === 'airport' && subcategory === 'dropoff') {
                    // 检查dropoff地址，如果不像是机场地址，尝试与pickup交换
                    if (!isAirportAddress(orderData['dropoff-address']) && isAirportAddress(orderData['pickup-address'])) {
                        // 交换地址
                        const temp = orderData['pickup-address'];
                        orderData['pickup-address'] = orderData['dropoff-address'];
                        orderData['dropoff-address'] = temp;
                        console.log('送机订单地址已交换');
                    }
                    // 确保送机订单的dropoff地址是机场
                    if (!isAirportAddress(orderData['dropoff-address'])) {
                        // 如果还不是机场地址，尝试从航班号和服务类型推断
                        if (orderData['flight-number']) {
                            // 有航班号，假设是KLIA
                            orderData['dropoff-address'] = '吉隆坡国际机场(KLIA Terminal 1)';
                            console.log('送机订单自动设置dropoff地址为KLIA');
                        }
                    }
                    
                    // 对机场地址进行翻译
                    if (isAirportAddress(orderData['dropoff-address'])) {
                        orderData['dropoff-address'] = translateAirportAddress(orderData['dropoff-address']);
                    }
                }
                
                return orderData;
            },
            
            /**
             * 调用API进行文本处理，使用Gemini模型
             * @param {string} orderText 预处理后的订单文本
             * @returns {Promise<Object>} 处理后的订单数据
             */
            processWithAPI: async function(orderText) {
                // 提取订单数据
                const extractedData = this.extractOrderData(orderText);
                console.log('初步提取的数据:', extractedData);
                
                // 处理地址信息
                const processedData = this.processAddress(extractedData);
                console.log('处理地址后的订单数据:', processedData);
                
                // 按照要求处理订单金额 - 订单金额 * 0.615
                if (processedData.price) {
                    const originalPrice = parseFloat(processedData.price);
                    if (!isNaN(originalPrice)) {
                        processedData.price = (originalPrice * 0.615).toFixed(2);
                    }
                }
                
                console.log('处理价格后的订单数据:', processedData);
                
                // 创建返回的订单数组
                const orders = [processedData];
                
                // 检查是否有附加服务，如果有则创建额外的订单
                if (processedData['add-service'] && processedData['add-service'].trim()) {
                    // 创建附加服务的新订单
                    const additionalOrder = { ...processedData };
                    // 设置固定价格25
                    additionalOrder.price = "25";
                    // 修改订单号，添加paging后缀
                    if (additionalOrder['ota-reference']) {
                        additionalOrder['ota-reference'] = additionalOrder['ota-reference'] + '-paging';
                    }
                    // 设置子类型为paging
                    additionalOrder.subcategory = 'paging';
                    // 在备注中指明这是一个接机牌订单
                    additionalOrder.remark = (additionalOrder.remark || '') + ' | 此单为接机牌服务';
                    
                    // 添加到订单数组
                    orders.push(additionalOrder);
                }
                
                console.log('最终处理的订单数组:', orders);
                return orders;
            },
            
            /**
             * 验证并清理API返回的结果，确保数据符合期望格式
             * @param {Object} orderData API返回的订单数据
             * @returns {Object} 验证并清理后的订单数据
             */
            validateAndSanitizeApiResult: function(orderData) {
                // 现有的验证逻辑
                return orderData;
            }
        };

        // -------------------------
        //      模块定义
        // -------------------------

        // 1. 应用程序状态
        const appState = {
            currentLanguage: 'zh',
            isEditing: false,
            multiOrderViewActive: false // 新增：标记多订单视图是否激活
        };

        // 2. DOM 元素引用
        const dom = {
            elements: {
                rawOrderTextarea: null,
                convertBtn: null,
                resetBtn: null,
                editBtn: null,
                saveBtn: null,
                cancelEditBtn: null, // 新增
                copyOutputBtn: null,
                formInputs: null,
                multiOrdersContainer: null,
                ordersList: null,
                notification: null,
                languageSelector: null,
                gridContainer: null,
                // 表单字段
                otaInput: null,
                otaReferenceInput: null,
                priceInput: null,
                nameInput: null,
                phoneInput: null,
                emailInput: null,
                flightNumberInput: null,
                pickupDatetimeInput: null,
                pickupAddressInput: null,
                dropoffAddressInput: null,
                carTypeInput: null,
                passengerNumberInput: null,
                languageInput: null,
                categorySelect: null,
                subcategorySelect: null,
                drivingRegionSelect: null,
                driverCountInput: null,
                remarkTextarea: null,
                apiLoader: null
            },
            
            // 获取元素的方法
            getElements: function() {
                this.elements.rawOrderTextarea = document.getElementById('raw-order');
                this.elements.convertBtn = document.getElementById('convert-btn');
                this.elements.resetBtn = document.getElementById('reset-btn');
                this.elements.editBtn = document.getElementById('edit-btn');
                this.elements.saveBtn = document.getElementById('save-btn');
                this.elements.cancelEditBtn = document.getElementById('cancel-edit-btn'); // 新增
                this.elements.copyOutputBtn = document.getElementById('copy-output');
                this.elements.formInputs = document.querySelectorAll('#order-form input, #order-form select, #order-form textarea');
                this.elements.multiOrdersContainer = document.getElementById('multi-orders-container');
                this.elements.ordersList = document.getElementById('orders-list');
                this.elements.notification = document.getElementById('notification');
                this.elements.languageSelector = document.getElementById('language-selector');
                this.elements.gridContainer = document.querySelector('.grid');
                // 表单字段
                this.elements.otaInput = document.getElementById('ota');
                this.elements.otaReferenceInput = document.getElementById('ota-reference');
                this.elements.priceInput = document.getElementById('price');
                this.elements.nameInput = document.getElementById('name');
                this.elements.phoneInput = document.getElementById('phone');
                this.elements.emailInput = document.getElementById('email');
                this.elements.flightNumberInput = document.getElementById('flight-number');
                this.elements.pickupDatetimeInput = document.getElementById('pickup-datetime');
                this.elements.pickupAddressInput = document.getElementById('pickup-address');
                this.elements.dropoffAddressInput = document.getElementById('dropoff-address');
                this.elements.carTypeInput = document.getElementById('car-type');
                this.elements.passengerNumberInput = document.getElementById('passenger-number');
                this.elements.languageInput = document.getElementById('language');
                this.elements.categorySelect = document.getElementById('category');
                this.elements.subcategorySelect = document.getElementById('subcategory');
                this.elements.drivingRegionSelect = document.getElementById('driving-region');
                this.elements.driverCountInput = document.getElementById('driver-count');
                this.elements.remarkTextarea = document.getElementById('remark');
                this.elements.apiLoader = document.getElementById('api-loader');
            },

            // UI 辅助方法
            showElement: function(element) {
                if (element) element.classList.remove('hidden');
            },
            
            hideElement: function(element) {
                if (element) element.classList.add('hidden');
            },
            
            showNotification: function(message, type = 'success') { // 添加 type 参数，默认为 'success'
                const notification = this.elements.notification;
                if (!notification) return;

                // 移除可能存在的旧类型类
                notification.classList.remove('success', 'error', 'warning', 'info');
                // 添加新的类型类
                notification.classList.add(type);

                notification.textContent = message;
                notification.classList.add('show');

                // 清除旧的计时器（如果有）
                if (notification.timerId) {
                    clearTimeout(notification.timerId);
                }

                // 设置新的计时器
                notification.timerId = setTimeout(() => {
                    notification.classList.remove('show');
                    // 动画结束后移除类型类，重置图标
                    // notification.addEventListener('transitionend', () => {
                    //    notification.classList.remove(type);
                    // }, { once: true });
                }, 4000); // 稍微延长显示时间
            },
            
            showLoader: function() {
                const loader = this.elements.apiLoader;
                if (loader) loader.classList.add('show');
            },
            
            hideLoader: function() {
                const loader = this.elements.apiLoader;
                if (loader) loader.classList.remove('show');
            }
        };

        // 3. 国际化 (i18n)
        const i18n = {
            strings: {
            zh: {
                // 页面标题
                "携程专车-original-order": "携程专车原始订单",
                "standardized-order": "标准化订单",
                "multiple-orders-detected": "已识别到的多个订单",
                // 表单标签和占位符
                "original-order-data": "原始订单数据",
                "paste-携程专车-order-data": "粘贴携程专车原始订单数据...",
                "ota-platform": "OTA平台",
                "order-number": "订单编号",
                "price": "价格",
                "passenger-name": "乘客姓名",
                "phone": "电话",
                "email": "邮箱",
                "flight-number": "航班号",
                "pickup-time": "接机时间",
                "pickup-address": "接机地址",
                "dropoff-address": "送达地址",
                "car-type": "车型",
                "luggage-count": "行李数量",
                "passenger-count": "乘客人数",
                "language": "语言",
                "order-type": "订单类型",
                "driving-region": "驾驶区域",
                "driver-count": "司机数量",
                "remarks": "备注",
                // 按钮
                "process-order": "处理订单",
                "reset": "重置",
                "enable-edit": "启用编辑",
                "save-changes": "保存更改",
                "copy-output": "复制输出",
                "view-details": "查看详细",
                "copy": "复制",
                "back-to-home": "返回首页",
                    "cancel-edit": "取消编辑", // 新增
                // 通知
                "please-enter-data": "请输入原始订单数据",
                "data-copied": "数据已复制到剪贴板",
                "edit-enabled": "已启用编辑模式",
                "changes-saved": "更改已保存",
                "multiple-orders-found": "检测到多个订单，正在处理...",
                    // 字段值
                    "airport-pickup": "机场接机",
                    "airport-dropoff": "机场送机",
                    "charter": "包车服务",
                    "cancel-edit": "取消编辑"
                },
                en: { /* 这里保留原来的英文文本 */ },
                jp: { /* 这里保留原来的日文文本 */ },
                ko: { /* 这里保留原来的韩文文本 */ }
            },
            
            updateUI: function(lang) {
                appState.currentLanguage = lang;
                
            // 更新标题
                document.title = `OTA Order Converter - ${this.strings[lang]["携程专车-original-order"] || '携程专车订单处理'}`;
            
            // 更新data-i18n属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                    if (this.strings[lang][key]) {
                        element.textContent = this.strings[lang][key];
                }
            });
            
            // 更新占位符
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                    if (this.strings[lang][key]) {
                        element.placeholder = this.strings[lang][key];
                }
            });
            
            // 保存当前语言到localStorage
            localStorage.setItem('preferred-language', lang);
            },
            
            getString: function(key) {
                const lang = appState.currentLanguage;
                return this.strings[lang][key] || key;
            }
        };

        // 4. API 交互
        const api = {
            processOrderWithGemini: async function(orderText) {
                console.log('使用Gemini API处理订单文本');
                dom.showLoader();
                let response; // 将response移到try外部，以便finally中访问

                try {
                    // 构建增强的提示词
                    const prompt = `
                    系统指令: 你是一个专业的旅游交通订单解析助手，精通接送机和包车业务。你的唯一任务是从文本中准确提取订单信息并以纯JSON格式返回。

                    严格输出规则:
                    1. 你必须只输出一个标准的JSON数组，不要输出任何其他文本、说明、注释或代码块标记（如 \`\`\`json ... \`\`\`）。
                    2. 输出的JSON必须保持良好格式化，所有键和字符串值必须用双引号包围。
                    3. 如果需要生成多个订单（例如，文本中包含接机和送机），将它们全部放在同一个JSON数组中。

                    字段提取优先级与规则:
                    1.  **核心字段 (必须提取，不能为空)**:
                        *   \`name\`: 乘客姓名 (从 "客人信息", "联系人", "乘客", "姓名" 等字段提取，如果没有，识别可能是华人名字的字段)。
                        *   \`pickup-address\`: 上车地址。(酒店名字翻译成官方英文名字，机场使用正规英文名字）。
                        *   \`dropoff-address\`: 下车地址。(酒店名字翻译成官方英文名字，机场使用正规英文名字）。
                        *   \`pickup-datetime\`: 上车日期和时间 (格式: "DD-MM-2025 HH:MM", 24小时制)。如果年份缺失，使用2025年。
                        *   \`vehicle-type\`: 车型 (必须使用下面的 "字段映射规则" 进行转换)。
                    2.  **机场接送必填字段**:
                        *   \`flight-number\`: 航班号 (仅当 \`category\` 为 "airport" 时必须提取)。
                    3.  **重要选填字段 (尽量提取，否则使用默认值逻辑)**:
                        *   \`phone\`: 乘客联系电话 (提取纯数字)。 **如果未找到，默认值为: "乘客姓名 + 上车日期(DDMM)"** (例如: "林亚贤0205")。
                        *   \`email\`: 乘客邮箱。
                        *   \`passenger-number\`: 乘客人数 (从 "x人", "x位", "Pax: x" 等格式提取, 默认为1)。
                        *   \`luggage-number\`: 行李数量 (从 "行李x", "Luggage: x" 等提取, 默认为0)。
                        *   \`price\`: 订单价格 (提取纯数字)。 **如果未找到，默认值为: "1"**。
                        *   \`ota-reference\`: OTA平台订单号。 **如果未找到，默认值为: "乘客姓名 + 上车日期(DDMM)"** (例如: "林亚贤0205")。
                        *   \`remark\`: 订单备注或特殊要求。 **如果文本中未找到明确的备注信息，使用以下默认备注**: "要用 携程司导端 工作。 this job need use china app"。
                    4.  **分类字段 (必须确定)**:
                        *   \`category\`: 订单类别。如果涉及航班号或机场地址，则为 "airport"；否则，如果文本包含 "包车"，则为 "charter"；其他情况默认为 "airport"。
                        *   \`subcategory\`: 订单子类别。根据关键词判断: "接机" -> "pickup", "送机" -> "dropoff", "包车" -> "charter"。如果 \`category\` 是 "airport" 但无法判断方向，默认为 "pickup"。
                        *   \`driving-region\`: 驾驶区域 (必须使用下面的 "字段映射规则" 进行转换，默认为 "kl")。
                    5.  **固定或默认字段**:
                        *   \`language\`: 乘客语言 (默认为 "chinese")。
                        *   \`ota\`: OTA平台 (默认为 "携程专车")。
                        *   \`driver-count\`: 司机数量 (默认值为 1)。

                    日期时间处理规则:
                    *   所有日期时间必须转换为 "DD-MM-YYYY HH:MM" 格式。
                    *   时间必须使用24小时制。
                    *   如果年份缺失，使用2025年。如果提取的日期明显早于2025年（例如，月份比当前月份小），则年份加一。
                    *   **处理航班信息中的"+1"标记：当航班信息格式为"MM.DD 航班号 HH:MM--HH:MM+1"时，"+1"表示航班在次日抵达。需要将实际接机日期处理为航班日期+1天。例如"5.06 MU2969 19:05--00:40+1"表示航班5月6日19:05起飞，5月7日00:40抵达，接机日期应为5月7日。**
                    *   **如果遇到航班时间格式，例如"航班号 HHMM-HHMM"、"航班号 HH:MM--HH:MM"或类似格式（分隔符可能是单连字符、双连字符或其他符号），它表示起飞和到达时间。对于接机(pickup)订单，使用第二个时间(到达时间)作为pickup-datetime的时间部分；对于送机(dropoff)订单，使用第一个时间(起飞时间)并减去3.5小时作为pickup-datetime的时间部分。注意识别和处理类似"5.06 MU2969 19:05--00:40+1"的格式，将日期正确关联到相应的时间点。**
                    *   **如果订单文本中明确包含"送机时间"字样并指定了具体时间，则直接使用该时间作为pickup-datetime，不需要减去3.5小时。**
                    *   **对于包车订单，如果未提供具体的开始时间，默认使用09:00。**
                    *   送机订单的pickup-datetime（如果不是通过HHMM-HHMM计算得到，且没有明确的"送机时间"），应该是航班起飞时间减去大约3.5小时。如果文本只提供了起飞时间，请进行计算。

                    多订单识别规则:
                    *   仔细检查文本中是否同时包含 "接机" 和 "送机" 的信息，或多个日期/航班号。
                    *   如果识别为多个订单，为每个订单生成一个独立的JSON对象，并将它们放入同一个数组中返回。每个订单对象都应遵循上述所有字段规则。

                    字段映射规则 (输出时必须使用映射后的值):
                    *   \`vehicle-type\`:
                        - "经济型/舒适型/五座/Sedan" → "sedan"
                        - "商务型/七座/MPV/Serena" → "Serena"
                        - "豪华型/七座/艾尔法/Alphard" → "Alphard"
                        - "商务型/九座/辛隆/Starex" → "Starex"
                        - "十五座/中巴/Van" → "Van"
                        - "SUV/七座/经济/越野" → "SUV"
                    *   \`driving-region\`:
                        - "吉隆坡/KLIA/雪兰莪" → "kl"
                        - "新加坡/樟宜机场/Changi" → "sg"
                        - "哥打京那巴鲁/亚庇/KK" → "kk"
                        - "仙本那/Semporna" → "sp"
                        - 默认值: "kl"

                    错误处理与补全规则:
                    *   如果缺少核心字段，请根据上下文合理推断或留空字符串 ""。
                    *   严格按照上述规则生成默认值。
                    *   清理提取到的文本，去除不必要的标点和空格。

                    纯JSON输出格式示例 (包含默认值逻辑):
                    [
                      {
                        "name": "林亚贤",
                        "phone": "林亚贤0205", // 默认值示例
                        "email": "",
                        "ota-reference": "林亚贤0205", // 默认值示例
                        "price": "1", // 默认值示例
                        "pickup-address": "KLIA Terminal 1",
                        "dropoff-address": "Ibis Hotel KL",
                        "pickup-datetime": "02-05-2025 00:40", // 使用2025年
                        "flight-number": "MU2969",
                        "category": "airport",
                        "subcategory": "pickup",
                        "driving-region": "kl",
                        "vehicle-type": "sedan", // 从"5座"映射
                        "passenger-number": "3", // 从"3人"提取
                        "luggage-number": "0", // 默认值
                        "language": "chinese",
                        "ota": "携程专车",
                        "driver-count": 1, // 默认值
                        "remark": "要用 携程司导端 工作。 this job need use china app" // 默认值示例
                      },
                      {
                         "name": "林亚贤",
                         "phone": "林亚贤0305", // 默认值示例
                         "email": "",
                         "ota-reference": "林亚贤0305", // 默认值示例
                         "price": "1", // 默认值示例
                         "pickup-address": "Ibis Hotel KL",
                         "dropoff-address": "KLIA Terminal 2", // 假设送机去T2
                         "pickup-datetime": "03-05-2025 10:00", // 使用2025年
                         "flight-number": "", // 包车送机可能无航班号
                         "category": "charter", // 包含包车
                         "subcategory": "charter", // 包含包车
                         "driving-region": "kl",
                         "vehicle-type": "sedan",
                         "passenger-number": "3",
                         "luggage-number": "0",
                         "language": "chinese",
                         "ota": "携程专车",
                         "driver-count": 1,
                         "remark": "(要用 携程司导端 工作。 this job need use china app)" // 默认值示例
                       }
                    ]

                    原始订单文本:
                    ${orderText}
                    `;

                    console.log('发送到API的原始文本:', orderText.substring(0, 200) + '...'); // 打印更多文本
                    console.log('构建的提示词:', prompt.substring(0, 300) + '...'); // 打印更多提示词

                    // 调用Gemini API
                    response = await fetch(
                        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${appState.apiKey}`, // 使用2.0 flash模型
                        {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify({
                                contents: [{ parts: [{ text: prompt }] }],
                                generationConfig: {
                                    responseMimeType: "application/json", // 要求返回JSON类型
                                    temperature: 0.1,
                                    // maxOutputTokens: 2048, // 移除或调整，让模型决定输出长度
                                    topP: 0.1,
                                    topK: 1,
                                }
                            })
                        }
                    );

                    // *** 新增调试日志 ***
                    console.log("API 响应状态:", response.status, response.statusText);
                    const responseBodyText = await response.text(); // 读取原始响应文本
                    console.log("API 原始响应体:", responseBodyText);

                    if (!response.ok) {
                        // 如果HTTP状态不是2xx，抛出错误，包含状态码和响应体
                         throw new Error(`API 请求失败，状态码: ${response.status} - ${response.statusText}. 响应体: ${responseBodyText}`);
                    }

                     // 尝试解析原始响应文本为JSON
                     const data = JSON.parse(responseBodyText);
                     console.log("Gemini API 返回结果 (已解析):", data);

                    // 验证基本结构
                    if (!data || !data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
                         // 尝试检查是否存在 blockReason 或 finishReason
                         if (data && data.candidates && data.candidates[0] && data.candidates[0].finishReason) {
                             console.warn("API返回内容被阻止或完成异常:", data.candidates[0].finishReason);
                             if (data.candidates[0].safetyRatings) {
                                 console.warn("安全评分:", data.candidates[0].safetyRatings);
                             }
                             throw new Error(`API未返回有效内容，完成原因: ${data.candidates[0].finishReason}`);
                         }
                         if (data && data.promptFeedback && data.promptFeedback.blockReason) {
                             console.warn("API请求被阻止:", data.promptFeedback.blockReason);
                             if (data.promptFeedback.safetyRatings) {
                                 console.warn("安全评分:", data.promptFeedback.safetyRatings);
                             }
                             throw new Error(`API请求被阻止，原因: ${data.promptFeedback.blockReason}`);
                         }
                         // 打印整个data对象以便进一步分析
                         console.error("API返回结果结构不符合预期:", data);
                         throw new Error('API返回结果格式不正确或内容缺失');
                     }

                    // Gemini 2.0 Flash 返回JSON时，parts[0].text可能就是JSON字符串本身
                    const resultJsonString = data.candidates[0].content.parts[0].text;
                    console.log('API返回的原始文本(JSON字符串):', resultJsonString);

                    // 直接解析这个JSON字符串
                    return JSON.parse(resultJsonString); // 移除了 parseGeminiResponse 调用

                } catch (error) {
                    console.error('处理订单文本时出错 (api.processOrderWithGemini catch):', error);
                     // 添加更多错误细节
                     if (error instanceof SyntaxError) {
                         console.error("JSON 解析错误详情:", error.message);
                     } else if (response) { // 如果有response对象
                         console.error("API响应状态（错误时）:", response.status, response.statusText);
                     }
                     // 重新抛出错误，让上层处理回退逻辑
                    throw error;
                } finally {
                    dom.hideLoader();
                }
            },

            // *** parseGeminiResponse 函数现在不再需要，因为我们直接解析API返回的JSON字符串 ***
            // parseGeminiResponse: function(resultText) { ... }

            // 从文本中提取数据的辅助方法 (保持不变)
            extractDataFromText: function(text) {
                console.log('尝试从文本手动提取数据 (增强分割逻辑)');
                const orders = [];
                let commonBlock = "";

                // *** 新增：优先查找强分隔符 ***
                const strongSeparatorRegex = /\n(?:-{3,}|\*{3,})\n/g; // 匹配 --- 或 *** 分隔行
                const blankLineSeparatorRegex = /\n\n\n+/g; // 匹配多个空行
                let segments = [];
                let usedStrongSeparator = false;

                if (strongSeparatorRegex.test(text)) {
                    segments = text.split(strongSeparatorRegex).map(s => s.trim()).filter(s => s.length > 0);
                    if (segments.length > 1) usedStrongSeparator = true;
                    console.log("尝试使用强分隔符分割:", segments.length);
                }

                if (!usedStrongSeparator && blankLineSeparatorRegex.test(text)) {
                     segments = text.split(blankLineSeparatorRegex).map(s => s.trim()).filter(s => s.length > 0);
                     if (segments.length > 1) usedStrongSeparator = true;
                     console.log("尝试使用空行分隔符分割:", segments.length);
                 }
                 // *** 强分隔符检查结束 ***

                // 如果强分隔符分割成功
                if (usedStrongSeparator) {
                    commonBlock = ""; // 强分隔符模式下，通常没有明确的共同块
                    for (let i = 0; i < segments.length; i++) {
                        const orderSegment = segments[i];
                        console.log(`处理强分隔段落 ${i + 1}:`, orderSegment.substring(0, 100) + "...");
                         let subcategoryHint = null; // 推断子类别
                         if (orderSegment.includes('接机')) subcategoryHint = 'pickup';
                         else if (orderSegment.includes('送机')) subcategoryHint = 'dropoff';
                         else if (orderSegment.includes('包车')) subcategoryHint = 'charter';

                        const rawOrderData = this.extractSingleOrderData(orderSegment, orderSegment, subcategoryHint); // fullText 和 orderText 相同
                        const validatedData = this.validateAndSanitizeApiResult(rawOrderData);
                        if (validatedData) orders.push(validatedData);
                        else console.warn(`强分隔段落 ${i + 1} 验证失败`);
                    }
                }
                // 否则，回退到日期标记分割
                else {
                    const dateMarkerRegex = /^(?:\d{1,2}[./-]\d{1,2}|\d{1,2}月\d{1,2}日?)\s*:/gm;
                    const matches = [...text.matchAll(dateMarkerRegex)];

                    if (matches.length === 0) {
                        console.log("未找到日期标记或强分隔符，按单订单处理");
                        const rawOrderData = this.extractSingleOrderData(text, text);
                         const validatedData = this.validateAndSanitizeApiResult(rawOrderData);
                         if (validatedData) orders.push(validatedData);
                    } else {
                        console.log(`找到 ${matches.length} 个日期标记，尝试日期分割`);
                         // ... (日期标记分割逻辑保持不变，但内部调用 validateAndSanitizeApiResult) ...
                         commonBlock = text.substring(0, matches[0].index).trim();
                         console.log("共同信息块:", commonBlock.substring(0, 100) + "...");
                         let lastIndex = matches[0].index;

                         for (let i = 0; i < matches.length; i++) {
                              const startIndex = matches[i].index;
                              const endIndex = (i === matches.length - 1) ? text.length : matches[i + 1].index;
                              const orderSegment = text.substring(startIndex, endIndex).trim();

                              if (orderSegment) {
                                  console.log(`处理日期标记段落 ${i + 1}:`, orderSegment.substring(0, 100) + "...");
                                  const combinedText = commonBlock ? `${commonBlock}\n${orderSegment}` : orderSegment;
                                  let subcategoryHint = null;
                                  if (orderSegment.includes('接机')) subcategoryHint = 'pickup';
                                  else if (orderSegment.includes('送机')) subcategoryHint = 'dropoff';
                                  else if (orderSegment.includes('包车')) subcategoryHint = 'charter';

                                  const rawOrderData = this.extractSingleOrderData(combinedText, orderSegment, subcategoryHint);
                                  const validatedData = this.validateAndSanitizeApiResult(rawOrderData); // 验证
                                  if (validatedData) orders.push(validatedData);
                                  else console.warn(`日期标记段落 ${i + 1} 验证失败`);
                              }
                         }
                    }
                }


                // 回退逻辑 (保持不变)
                if (orders.length === 0) {
                     console.warn("本地分割/提取后未能生成任何有效订单，返回空数组");
                     return [];
                 }

                console.log(`本地提取共完成 ${orders.length} 个订单`);
                return orders;
            },

            // 提取单个订单数据 (保持不变)
            extractSingleOrderData: function(fullText, orderText, forcedSubcategory) {
                 const text = orderText || fullText; // 优先使用分割后的订单文本进行特定字段提取
                 console.log(`开始本地提取 ${forcedSubcategory ? forcedSubcategory : '单'}订单数据...`);

                 const data = {
                     "name": "",
                     "phone": "", // 新增
                     "email": "", // 新增
                     "ota-reference": "", // 新增
                     "price": "1", // 保持默认值，但会尝试提取
                     "pickup-address": "",
                     "dropoff-address": "",
                     "pickup-datetime": "",
                     "flight-number": "",
                     "category": "airport", // 默认机场
                     "subcategory": forcedSubcategory || "pickup", // 使用强制或默认接机
                     "driving-region": "kl", // 默认吉隆坡
                     "vehicle-type": "sedan", // 默认五座
                     "passenger-number": "1", // 默认1人
                     "luggage-number": "0", // 新增，默认0
                     "language": "chinese", // 默认中文
                     "ota": "携程专车", // 默认平台
                     "remark": "" // 新增
                 };

                 // 提取姓名 (使用更宽泛的匹配)
                 const nameMatch = fullText.match(/(?:联系人|客人|乘客|姓名|Name|Passenger)[：:\\s]*([^\\n,，]+)/i);
                 if (nameMatch) data.name = nameMatch[1].trim();

                 // 提取电话 (匹配多种格式，清理非数字字符)
                 const phoneMatch = fullText.match(/(?:电话|手机|Phone|Tel)[：:\\s]*([+]?[\d\s-()]{8,})/i);
                 if (phoneMatch) data.phone = phoneMatch[1].replace(/[^\d+]/g, ''); // 只保留数字和+

                 // 提取邮箱
                 const emailMatch = fullText.match(/(?:邮箱|Email)[：:\\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i);
                 if (emailMatch) data.email = emailMatch[1].trim();

                 // 提取OTA订单号 (匹配多种可能的标签和格式)
                 const otaRefMatch = fullText.match(/(?:订单编号|Order No|Reference|订单号)[：:\\s]*([\w-]+)/i);
                 if (otaRefMatch) data["ota-reference"] = otaRefMatch[1].trim();

                 // 提取价格 (匹配数字，可能带货币符号或单位)
                 const priceMatch = fullText.match(/(?:价格|费用|Price|Fee)[：:\\s]*([¥$RM]?\s*\d+(?:[.,]\d+)?)/i);
                 if (priceMatch) {
                     const priceValue = priceMatch[1].match(/[\d.,]+/); // 提取数字和分隔符
                     if (priceValue) {
                         data.price = priceValue[0].replace(/,/g, '.'); // 将逗号替换为点
                     }
                 }

                 // 提取航班信息 (优先使用与接送机明确关联的)
                 let flightMatch = null;
                 const flightRegex = /([A-Z]{2,3}\d{1,4})/i;
                 if (forcedSubcategory === "pickup") {
                     flightMatch = text.match(new RegExp(`接机[\\s\\S]*?${flightRegex.source}`, 'i'));
                 } else if (forcedSubcategory === "dropoff") {
                     flightMatch = text.match(new RegExp(`送机[\\s\\S]*?${flightRegex.source}`, 'i'));
                 }
                 // 如果没有明确关联，则从整个文本查找
                 if (!flightMatch) {
                      flightMatch = fullText.match(/(?:航班号|Flight No)[：:\\s]*([A-Z]{2,3}\d{1,4})/i) || fullText.match(flightRegex);
                 }
                 if (flightMatch) data["flight-number"] = flightMatch[1].trim().toUpperCase();

                 // 提取接机/送机时间 (增强日期和时间格式匹配)
                 let extractedDateStr = null;
                 let extractedTimeStr = null; // HH:MM 格式
                 let explicitPickupTimeStr = null; // 显式上车时间 HH:MM
                 let departureTimeStr = null; // 起飞时间 HH:MM (用于送机计算)
                 let hasExplicitSendingTime = false; // 新增：标记是否有明确的"送机时间"

                 // 1. 尝试从特定订单段落 (orderText) 中提取日期标记 (如 5.02 :)
                 const dateMarkerInSegmentMatch = orderText ? orderText.match(/^(?:(\d{1,2})[./-月](\d{1,2}))[./-日]?\s*:/) : null;
                 if (dateMarkerInSegmentMatch) {
                      const month = dateMarkerInSegmentMatch[1].padStart(2, '0');
                      const day = dateMarkerInSegmentMatch[2].padStart(2, '0');
                      const year = 2025; // 使用固定年份2025而不是当前年份
                      extractedDateStr = `${day}-${month}-${year}`;
                      console.log("从段落标记提取日期:", extractedDateStr);
                 } else {
                      // 2. 如果段落无标记，尝试从完整文本 (fullText) 提取第一个日期标记作为基础日期
                      const firstDateMarkerMatch = fullText.match(/(?:^|\n)(\d{1,2})[./-月](\d{1,2})[./-日]?\s*:/);
                       if (firstDateMarkerMatch) {
                           const month = firstDateMarkerMatch[1].padStart(2, '0');
                           const day = firstDateMarkerMatch[2].padStart(2, '0');
                           const year = 2025; // 使用固定年份2025而不是当前年份
                           extractedDateStr = `${day}-${month}-${year}`;
                           console.log("从全文首个标记提取日期:", extractedDateStr);
                       }
                 }

                 // *** 新增：3. 优先查找明确的"送机时间"或"接机时间" ***
                 const sendingTimeMatch = text.match(/(?:送机时间|送机\s+时间|送机.*?时间)[：:]\s*(\d{1,2}[:.：]\d{1,2})/i);
                 if (sendingTimeMatch) {
                     const timeParts = sendingTimeMatch[1].match(/(\d{1,2})[:.：](\d{1,2})/);
                     if (timeParts) {
                        hasExplicitSendingTime = true; // 标记有明确的送机时间
                        explicitPickupTimeStr = `${timeParts[1].padStart(2, '0')}:${timeParts[2].padStart(2, '0')}`;
                        console.log("找到明确的送机时间:", explicitPickupTimeStr);
                        extractedTimeStr = explicitPickupTimeStr; // 直接使用送机时间
                     }
                 }
                 
                 // 接机时间
                 const pickupTimeMatch = text.match(/(?:接机时间|接机\s+时间|接机.*?时间)[：:]\s*(\d{1,2}[:.：]\d{1,2})/i);
                 if (pickupTimeMatch && !hasExplicitSendingTime) {
                     const timeParts = pickupTimeMatch[1].match(/(\d{1,2})[:.：](\d{1,2})/);
                     if (timeParts) {
                        explicitPickupTimeStr = `${timeParts[1].padStart(2, '0')}:${timeParts[2].padStart(2, '0')}`;
                        console.log("找到明确的接机时间:", explicitPickupTimeStr);
                        extractedTimeStr = explicitPickupTimeStr; // 直接使用接机时间
                     }
                 }
                 
                 // 4. 通用的上车时间
                 if (!hasExplicitSendingTime && !extractedTimeStr) {
                     const explicitTimeMatch = text.match(/(?:上车时间|Pickup Time|用车时间)[：:\\s]*(\d{1,2}[:.：]\d{1,2})/i);
                     if (explicitTimeMatch) {
                         const timeParts = explicitTimeMatch[1].match(/(\d{1,2})[:.：](\d{1,2})/);
                         if (timeParts) {
                            explicitPickupTimeStr = `${timeParts[1].padStart(2, '0')}:${timeParts[2].padStart(2, '0')}`;
                            console.log("找到显式上车时间:", explicitPickupTimeStr);
                            extractedTimeStr = explicitPickupTimeStr; // 直接使用显式时间
                         }
                     }
                 }

                 // 5. 如果没有明确时间，尝试提取其他时间格式
                 if (!extractedTimeStr && !hasExplicitSendingTime) {
                     // 尝试提取各种航班时间格式
                     // 支持的格式: HHMM-HHMM, HH:MM-HH:MM, HH:MM--HH:MM, 以及带+1的格式
                     const flightTimePatterns = [
                         { regex: /(?:[A-Z]{2,3}\d{1,4})\s+(\d{4})-(\d{4})(\+1)?/, isHHMM: true }, // HHMM-HHMM格式
                         { regex: /(?:[A-Z]{2,3}\d{1,4})\s+(\d{1,2}):(\d{2})\s*[-—–]\s*(\d{1,2}):(\d{2})(\+1)?/, isHHMM: false }, // HH:MM-HH:MM格式
                         { regex: /(?:[A-Z]{2,3}\d{1,4})\s+(\d{1,2}):(\d{2})\s*--\s*(\d{1,2}):(\d{2})(\+1)?/, isHHMM: false } // HH:MM--HH:MM格式
                     ];
                     
                     let departureTime = null;
                     let arrivalTime = null; 
                     let hasNextDayArrival = false; // 标记是否有+1
                     
                     // 遍历所有格式进行匹配
                     for (const pattern of flightTimePatterns) {
                         const match = text.match(pattern.regex);
                         if (match) {
                             console.log(`找到航班时间格式 ${pattern.isHHMM ? 'HHMM-HHMM' : 'HH:MM-HH:MM'}: ${match[0]}`);
                             
                             if (pattern.isHHMM) {
                                 // HHMM-HHMM 格式
                                 const departureHHMM = match[1];
                                 const arrivalHHMM = match[2];
                                 departureTime = `${departureHHMM.substring(0, 2)}:${departureHHMM.substring(2, 4)}`;
                                 arrivalTime = `${arrivalHHMM.substring(0, 2)}:${arrivalHHMM.substring(2, 4)}`;
                             } else {
                                 // HH:MM-HH:MM 格式
                                 const departureHour = match[1].padStart(2, '0');
                                 const departureMinute = match[2].padStart(2, '0');
                                 const arrivalHour = match[3].padStart(2, '0');
                                 const arrivalMinute = match[4].padStart(2, '0');
                                 departureTime = `${departureHour}:${departureMinute}`;
                                 arrivalTime = `${arrivalHour}:${arrivalMinute}`;
                             }
                             
                             // 检查是否有+1标记
                             hasNextDayArrival = match[match.length - 1] === '+1';
                             if (hasNextDayArrival) {
                                 console.log("检测到次日到达标记 (+1)");
                             }
                             
                             break; // 一旦匹配成功就跳出循环
                         }
                     }
                     
                     // 如果成功匹配到时间格式
                     if (departureTime && arrivalTime) {
                         if (forcedSubcategory === 'pickup' || text.includes('接机')) {
                             extractedTimeStr = arrivalTime;
                             console.log("判定为接机，使用到达时间:", extractedTimeStr);
                             
                             // 处理次日到达 (+1) 情况
                             if (hasNextDayArrival && extractedDateStr) {
                                 // 需要将pickup日期增加一天
                                 const dateParts = extractedDateStr.split('-');
                                 if (dateParts.length === 3) {
                                     const day = parseInt(dateParts[0], 10);
                                     const month = parseInt(dateParts[1], 10) - 1; // JS月份从0开始
                                     const year = parseInt(dateParts[2], 10);
                                     const nextDay = new Date(Date.UTC(year, month, day));
                                     nextDay.setUTCDate(nextDay.getUTCDate() + 1); // 加一天
                                     
                                     // 更新日期字符串
                                     extractedDateStr = `${nextDay.getUTCDate().toString().padStart(2, '0')}-${(nextDay.getUTCMonth() + 1).toString().padStart(2, '0')}-${nextDay.getUTCFullYear()}`;
                                     console.log("接机调整为次日到达，更新日期:", extractedDateStr);
                                 }
                             }
                         } else if (forcedSubcategory === 'dropoff' || text.includes('送机')) {
                             departureTimeStr = departureTime; // 记录起飞时间待计算
                             console.log("判定为送机，记录起飞时间:", departureTimeStr);
                             // 注意：这里不直接赋值给 extractedTimeStr，等待后续计算
                         } else {
                             extractedTimeStr = arrivalTime;
                             console.log("无法明确接送，默认使用到达时间:", extractedTimeStr);
                             
                             // 同样处理次日到达情况
                             if (hasNextDayArrival && extractedDateStr) {
                                 const dateParts = extractedDateStr.split('-');
                                 if (dateParts.length === 3) {
                                     const day = parseInt(dateParts[0], 10);
                                     const month = parseInt(dateParts[1], 10) - 1;
                                     const year = parseInt(dateParts[2], 10);
                                     const nextDay = new Date(Date.UTC(year, month, day));
                                     nextDay.setUTCDate(nextDay.getUTCDate() + 1);
                                     
                                     extractedDateStr = `${nextDay.getUTCDate().toString().padStart(2, '0')}-${(nextDay.getUTCMonth() + 1).toString().padStart(2, '0')}-${nextDay.getUTCFullYear()}`;
                                     console.log("默认调整为次日到达，更新日期:", extractedDateStr);
                                 }
                             }
                         }
                     }

                     // 尝试提取标准 HH:MM 格式（如果上面的匹配都失败）
                     if (!extractedTimeStr && !departureTimeStr) {
                         const timeRegex = /(\d{1,2})[:.：](\d{1,2})/;
                         let timePartMatch = orderText ? orderText.match(timeRegex) : null;
                         if (!timePartMatch) timePartMatch = fullText.match(timeRegex);

                         if (timePartMatch) {
                             const hour = timePartMatch[1].padStart(2, '0');
                             const minute = timePartMatch[2].padStart(2, '0');
                             extractedTimeStr = `${hour}:${minute}`;
                             console.log("提取到标准时间:", extractedTimeStr);
                              // 如果是送机，且未指定 forcedSubcategory，将此时间视为起飞时间
                              if (forcedSubcategory === 'dropoff' || (text.includes('送机') && !forcedSubcategory)) {
                                   departureTimeStr = extractedTimeStr;
                                   console.log("送机情况，记录标准时间为起飞时间:", departureTimeStr);
                                   extractedTimeStr = null; // 清空，等待计算
                              }
                         }
                     }
                 }

                 // 6. 处理包车订单默认时间
                 const isCharterOrder = forcedSubcategory === 'charter' || text.includes('包车');
                 if (isCharterOrder && !extractedTimeStr) {
                     extractedTimeStr = "09:00"; // 包车默认为上午9点
                     console.log("包车订单设置默认时间 09:00");
                 }

                 // 7. 组合日期和时间，处理送机计算
                 if (extractedDateStr) {
                     // 情况A: 有明确送机时间、显式上车时间或已确定接机时间
                     if (hasExplicitSendingTime || extractedTimeStr) {
                         data["pickup-datetime"] = `${extractedDateStr} ${hasExplicitSendingTime ? explicitPickupTimeStr : extractedTimeStr}`;
                         console.log("使用明确的时间:", data["pickup-datetime"]);
                     }
                     // 情况B: 是送机，无明确送机时间，但有起飞时间需要计算
                     else if (!hasExplicitSendingTime && departureTimeStr && (forcedSubcategory === 'dropoff' || text.includes('送机'))) {
                         const calculatedPickupTime = this.calculatePickupTimeForDropoff(departureTimeStr, extractedDateStr);
                         if (calculatedPickupTime) {
                             data["pickup-datetime"] = calculatedPickupTime;
                             console.log("送机计算上车时间:", data["pickup-datetime"]);
                         } else {
                             // 计算失败，至少保留日期和原始起飞时间
                             console.warn("送机上车时间计算失败");
                             data["pickup-datetime"] = ""; // 或者 `${extractedDateStr} ${departureTimeStr} (未计算)`;
                         }
                     } else {
                          console.warn("有日期但未能确定有效时间");
                     }
                 } else {
                     console.warn("未能成功提取有效日期");
                 }

                 if(data["pickup-datetime"]) {
                     console.log("最终组合pickup-datetime:", data["pickup-datetime"]);
                 } else {
                      console.warn("未能生成有效的 pickup-datetime");
                 }
                 // --- 日期时间提取结束 ---

                 // 提取地址信息
                 const hotelMatch = fullText.match(/(?:酒店|地址|Address)[：:\\s]*([^\\n]+)/i);
                  const airportRegex = /(机场|Airport|KLIA[12]?)/i;
                  // 尝试提取明确的上下车地址
                  const pickupAddrMatch = fullText.match(/(?:上车地点|接机地点|Pickup Address)[：:\\s]*([^\n]+)/i);
                  const dropoffAddrMatch = fullText.match(/(?:下车地点|送达地点|送机地点|Dropoff Address)[：:\\s]*([^\n]+)/i);

                  if (pickupAddrMatch) data["pickup-address"] = pickupAddrMatch[1].trim();
                  if (dropoffAddrMatch) data["dropoff-address"] = dropoffAddrMatch[1].trim();

                 if (!data["pickup-address"] || !data["dropoff-address"]) { // 如果没有明确提取到上下车地址
                      if (hotelMatch) {
                          const hotelAddress = hotelMatch[1].trim();
                          if (forcedSubcategory === "pickup" || text.includes('接机')) {
                              data["pickup-address"] = data["pickup-address"] || "KLIA机场"; // 优先使用已提取的
                              data["dropoff-address"] = data["dropoff-address"] || hotelAddress;
                              data.subcategory = "pickup"; // 确保子类别正确
                          } else if (forcedSubcategory === "dropoff" || text.includes('送机')) {
                              data["pickup-address"] = data["pickup-address"] || hotelAddress;
                              data["dropoff-address"] = data["dropoff-address"] || "KLIA机场";
                              data.subcategory = "dropoff";
                          } else {
                              // 无法确定方向时，检查酒店地址是否像机场
                              if (airportRegex.test(hotelAddress)) { // 如果酒店像机场，认为是送机
                                  data["pickup-address"] = data["pickup-address"] || "吉隆坡市区";
                                  data["dropoff-address"] = data["dropoff-address"] || hotelAddress;
                                  data.subcategory = "dropoff";
                              } else { // 默认接机
                                  data["pickup-address"] = data["pickup-address"] || "KLIA机场";
                                  data["dropoff-address"] = data["dropoff-address"] || hotelAddress;
                                  data.subcategory = "pickup";
                              }
                          }
                      } else { // 没有酒店信息，使用默认值
                          if (forcedSubcategory === "pickup" || text.includes('接机')) {
                              data["pickup-address"] = data["pickup-address"] || "KLIA机场";
                              data["dropoff-address"] = data["dropoff-address"] || "吉隆坡市区";
                              data.subcategory = "pickup";
                          } else if (forcedSubcategory === "dropoff" || text.includes('送机')) {
                              data["pickup-address"] = data["pickup-address"] || "吉隆坡市区";
                              data["dropoff-address"] = data["dropoff-address"] || "KLIA机场";
                              data.subcategory = "dropoff";
                          } else {
                              // 未明确接送机时，默认接机
                               data["pickup-address"] = data["pickup-address"] || "KLIA机场";
                               data["dropoff-address"] = data["dropoff-address"] || "吉隆坡市区";
                               data.subcategory = "pickup";
                          }
                      }
                  }


                 // 提取车型 (更全面的关键词)
                 const carTypeMatch = fullText.match(/(?:车型|Vehicle|Car Type)[：:\\s]*([^\\n]+)/i);
                 if (carTypeMatch) {
                     const carType = carTypeMatch[1].trim().toLowerCase();
                     // 标准化车型
                     if (carType.includes('五座') || carType.includes('经济') || carType.includes('sedan')) {
                         data["vehicle-type"] = "sedan"; // 五座标准化为sedan
                     } else if (carType.includes('七座') || carType.includes('mpv')) {
                         data["vehicle-type"] = "mpv"; // 七座标准化为mpv
                     } else if (carType.includes('商务') || carType.includes('serena')) {
                         data["vehicle-type"] = "mpv"; // 商务七座也标准化为mpv
                     } else if (carType.includes('九座') || carType.includes('starex')) {
                         data["vehicle-type"] = "mpv"; // 九座也标准化为mpv
                     } else if (carType.includes('豪华') || carType.includes('艾尔法') || carType.includes('alphard')) {
                         data["vehicle-type"] = "mpv"; // 豪华七座也标准化为mpv
                     } else if (carType.includes('van') || carType.includes('中巴') || carType.includes('十五座')) {
                         data["vehicle-type"] = "Van"; // 保留大型车标记
                     } else if (carType.includes('suv')) {
                         data["vehicle-type"] = "mpv"; // SUV也映射为七座mpv
                     }
                 }

                 // 提取乘客人数
                 const passengerMatch = fullText.match(/(?:人数|Pax|Passengers)[：:\\s]*(\d+)/i) ||
                                    fullText.match(/(\d+)\s*(?:人|位|pax)/i);
                 if (passengerMatch) {
                     data["passenger-number"] = passengerMatch[1].trim();
                 }

                 // 提取行李数量
                 const luggageMatch = fullText.match(/(?:行李|Luggage)[：:\\s]*(\d+)/i);
                 if (luggageMatch) {
                     data["luggage-number"] = luggageMatch[1].trim();
                 }

                 // 提取备注 (匹配备注、Remark或多行文本，更智能地判断结束位置)
                 const remarkMatch = fullText.match(/(?:备注|Remark|Notes)[：:\\s]*([\s\S]+)/i);
                 if (remarkMatch) {
                     let remarkText = remarkMatch[1].trim();
                     // 查找下一个可能的字段开始的标记，或者段落分隔符
                     const endMarkers = ['姓名', '电话', '邮箱', '航班', '日期', '时间', '酒店', '地址', '车型', '人数', '行李', '价格', '订单', 'Name', 'Phone', 'Email', 'Flight', 'Date', 'Time', 'Hotel', 'Address', 'Vehicle', 'Pax', 'Luggage', 'Price', 'Order', 'Reference', '\\n\\n', '-----'];
                     let endIndex = remarkText.length;
                     for (const marker of endMarkers) {
                         const regex = new RegExp(`(?:^|\\n)${marker}[：:\\s]`, 'i'); // 匹配行首或换行后的标记
                         const match = remarkText.match(regex);
                         if (match && match.index < endIndex) {
                             endIndex = match.index;
                         }
                     }
                     data.remark = remarkText.substring(0, endIndex).trim();
                 }

                 // 确定最终的 category 和 subcategory
                  if (data["flight-number"]) { // 有航班号，基本确定是机场接送
                      data.category = "airport";
                      // 如果未强制，根据地址或关键词判断接送
                      if (!forcedSubcategory) {
                          if (data["pickup-address"] && airportRegex.test(data["pickup-address"])) {
                             data.subcategory = "pickup"; // 从机场出发是接机
                         } else if (data["dropoff-address"] && airportRegex.test(data["dropoff-address"])) {
                             data.subcategory = "dropoff"; // 到机场结束是送机
                         } else if (text.includes('送机')) {
                             data.subcategory = "dropoff";
                         } else {
                             data.subcategory = "pickup"; // 默认接机
                         }
                     }
                 } else { // 没有航班号，可能是包车或其他
                      // 检查地址是否都不含机场，且文本包含"包车"
                      if (!airportRegex.test(data["pickup-address"]) && !airportRegex.test(data["dropoff-address"]) && fullText.includes('包车')) {
                         data.category = "charter";
                         data.subcategory = "charter";
                      } else { // 否则还是按机场逻辑（可能地址没提取对）
                         data.category = "airport";
                         data.subcategory = forcedSubcategory || "pickup"; // 默认接机
                      }
                  }
                  // 如果强制指定了子类别，则覆盖
                  if (forcedSubcategory) {
                      data.subcategory = forcedSubcategory;
                      data.category = (forcedSubcategory === 'pickup' || forcedSubcategory === 'dropoff') ? 'airport' : 'charter';
                  }

                  // 当类型为接机时，如果上车地址为空，则默认设置为机场
                  if (data.category === 'airport' && data.subcategory === 'pickup' && (!data["pickup-address"] || data["pickup-address"].trim() === '')) {
                      // 根据驾驶区域设置不同的机场
                      if (data["driving-region"] === 'sg') {
                          data["pickup-address"] = 'Singapore Changi Airport';
                      } else if (data["driving-region"] === 'kk') {
                          data["pickup-address"] = 'Kota Kinabalu International Airport';
                      } else {
                          data["pickup-address"] = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                      }
                      console.log('接机订单：自动设置上车地址为机场:', data["pickup-address"]);
                  }

                 console.log('本地提取完成:', data);
                 return data;
             },
             // 验证本地提取的数据是否足够基础有效 (可选，简单检查name和datetime)
              validateOrderData: function(data, isLocalExtraction = false) {
                 if (!data) return false;
                 const checkFields = ['name', 'pickup-datetime']; // 本地提取时要求较低
                 if (!isLocalExtraction) {
                      checkFields.push('pickup-address', 'dropoff-address');
                 }
                 for (const field of checkFields) {
                     if (!data[field] || data[field].toString().trim() === '') {
                         console.log(`订单数据缺少字段: ${field} (本地检查: ${isLocalExtraction})`);
                         return false;
                     }
                 }
                 // 可选：更严格的检查
                 return true;
             },

            // *** 新增：验证和清理API返回的订单数据 ***
            validateAndSanitizeApiResult: function(orderData) {
                if (!orderData || typeof orderData !== 'object') return null;

                const sanitizedData = { ...orderData };
                const warnings = [];
                let isPotentiallyValid = true; // 新增标记

                // 字段验证规则
                const fieldRules = {
                    name: { type: 'string', required: true },
                    phone: { type: 'string', format: /^\+?\d+$/ },
                    email: { type: 'string', format: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
                    'ota-reference': { type: 'string' },
                    price: { type: 'string', format: /^\d+(\.\d+)?$/ },
                    'pickup-address': { type: 'string', required: true },
                    'dropoff-address': { type: 'string', required: false }, // 修改为非必需，我们会尝试推断
                    'pickup-datetime': { type: 'string', format: /^\d{2}-\d{2}-\d{4} \d{2}:\d{2}$/, required: true },
                    'flight-number': { type: 'string', format: /^[A-Z0-9]+$/i },
                    category: { type: 'string', enum: ['airport', 'charter'], required: true },
                    subcategory: { type: 'string', enum: ['pickup', 'dropoff', 'charter'], required: true },
                    'driving-region': { type: 'string', enum: ['kl', 'sg', 'kk', 'sp', 'penang', 'jb'] },
                    'vehicle-type': { type: 'string', enum: ['sedan', 'mpv', 'Serena', 'Alphard', 'Starex', 'Van', 'SUV'], required: true },
                    'passenger-number': { type: 'string', format: /^\d+$/ },
                    'luggage-number': { type: 'string', format: /^\d+$/ },
                    language: { type: 'string' },
                    ota: { type: 'string' },
                    'driver-count': { type: 'number', integer: true }, // API可能返回数字
                    remark: { type: 'string' }
                };

                // 对每个字段进行检查
                for (const key in fieldRules) {
                    const rule = fieldRules[key];
                    // 只检查返回的字段，忽略未返回字段
                    if (sanitizedData[key] !== undefined) {
                        if (rule.required && (!sanitizedData[key] || sanitizedData[key].toString().trim() === '')) {
                            warnings.push(`必需字段 '${key}' 缺失或为空`);
                            console.log(`必需字段 '${key}' 缺失或为空`);
                            if (key !== 'dropoff-address') { // 允许dropoff-address为空
                                isPotentiallyValid = false;
                            }
                        }
                        if (sanitizedData[key] && rule.format && !rule.format.test(sanitizedData[key])) {
                            warnings.push(`字段 '${key}' (${sanitizedData[key]}) 格式严重错误`);
                            if(['phone', 'email'].includes(key)) {
                                // 这些非关键字段格式错误不标记整个订单为无效 
                                console.log(`注意: 字段 '${key}' 格式不正确，但不影响订单处理`);
                            } else {
                                isPotentiallyValid = false;
                            }
                        }
                        if (rule.enum && !rule.enum.includes(sanitizedData[key])) {
                            warnings.push(`字段 '${key}' (${sanitizedData[key]}) 值无效`);
                            if(['category', 'subcategory', 'vehicle-type'].includes(key)) isPotentiallyValid = false; // 这些分类错误比较严重
                            // 可以赋默认值或删除
                            // sanitizedData[key] = rule.enum[0];
                        }
                    }
                }
                
                // 机场订单检查航班号 (作为警告，不标记为无效)
                if (sanitizedData.category === 'airport' && (!sanitizedData['flight-number'] || sanitizedData['flight-number'].trim() === '')) {
                    warnings.push('机场订单缺少航班号');
                }

                // 标准化车型字段
                if (sanitizedData['vehicle-type']) {
                    const vehicleType = sanitizedData['vehicle-type'].toLowerCase();
                    // 五座车型标准化为sedan
                    if (vehicleType.includes('五座') || vehicleType === 'sedan' || vehicleType === '5座' || vehicleType === '5人座') {
                        sanitizedData['vehicle-type'] = 'sedan';
                    } 
                    // 七座车型标准化为mpv
                    else if (vehicleType.includes('七座') || vehicleType === '7座' || vehicleType === '7人座' || 
                             vehicleType === 'suv' || vehicleType === 'serena' || vehicleType === 'starex' || 
                             vehicleType === 'alphard' || vehicleType === 'mpv') {
                        sanitizedData['vehicle-type'] = 'mpv';
                    }
                    // 确保车型与系统兼容
                    const validVehicleTypes = ['sedan', 'mpv', 'Van', 'SUV'];
                    if (!validVehicleTypes.includes(sanitizedData['vehicle-type'])) {
                        console.log(`未识别的车型 "${sanitizedData['vehicle-type']}"，使用默认值：sedan`);
                        sanitizedData['vehicle-type'] = 'sedan';
                    }
                } else {
                    // 默认五座车型
                    sanitizedData['vehicle-type'] = 'sedan';
                    console.log('缺少车型字段，使用默认值：sedan');
                }

                // 尝试修复dropoff-address为空的情况
                if (sanitizedData.category === 'airport' && (!sanitizedData['dropoff-address'] || sanitizedData['dropoff-address'] === '')) {
                    if (sanitizedData.subcategory === 'pickup') {
                        // 接机订单：从pickup-address推断酒店信息
                        const hotelKeywords = ['酒店', '公寓', 'Hotel', 'Inn', 'Suites', 'Apartment'];
                        if (sanitizedData['pickup-address'].match(/(?:KLIA|机场|Airport)/i)) {
                            // 如果pickup-address是机场，无法推断dropoff-address
                            warnings.push('接机订单缺少目的地地址');
                        } else {
                            // 可能pickup-address已包含酒店信息，可以复用
                            sanitizedData['dropoff-address'] = sanitizedData['pickup-address'];
                            console.log(`接机订单: 使用pickup-address作为dropoff-address: ${sanitizedData['dropoff-address']}`);
                        }
                    } else if (sanitizedData.subcategory === 'dropoff') {
                        // 送机订单：设置为机场
                        const airport = sanitizedData['driving-region'] === 'sg' ? 'Singapore Changi Airport' : 
                                       sanitizedData['driving-region'] === 'kk' ? 'Kota Kinabalu International Airport' :
                                       'Kuala Lumpur International Airport';
                        sanitizedData['dropoff-address'] = airport;
                        console.log(`送机订单: 设置dropoff-address为标准机场: ${airport}`);
                    }
                }

                // 确保category和subcategory正确设置
                if (!sanitizedData.category || !sanitizedData.subcategory) {
                    // 如果category或subcategory为空，尝试根据上下文推断
                    if (sanitizedData['flight-number']) {
                        sanitizedData.category = 'airport';
                        // 根据地址判断接机或送机
                        if (sanitizedData['pickup-address'] && sanitizedData['pickup-address'].match(/(?:KLIA|机场|Airport)/i)) {
                            sanitizedData.subcategory = 'pickup';
                            console.log('根据上车地址判断为接机');
                        } else if (sanitizedData['dropoff-address'] && sanitizedData['dropoff-address'].match(/(?:KLIA|机场|Airport)/i)) {
                            sanitizedData.subcategory = 'dropoff';
                            console.log('根据下车地址判断为送机');
                        } else {
                            sanitizedData.subcategory = 'pickup'; // 默认接机
                            console.log('有航班号但无法判断接送机方向，默认为接机');
                        }
                    } else {
                        // 无航班号，可能是包车
                        sanitizedData.category = 'charter';
                        sanitizedData.subcategory = 'charter';
                        console.log('无航班号，设置为包车');
                    }
                }

                // 如果是接机订单且上车地址为空，自动设置为机场
                if (sanitizedData.category === 'airport' && sanitizedData.subcategory === 'pickup' && 
                    (!sanitizedData['pickup-address'] || sanitizedData['pickup-address'] === '')) {
                    const airport = sanitizedData['driving-region'] === 'sg' ? 'Singapore Changi Airport' : 
                                   sanitizedData['driving-region'] === 'kk' ? 'Kota Kinabalu International Airport' :
                                   'Kuala Lumpur International Airport (KLIA/KLIA2)';
                    sanitizedData['pickup-address'] = airport;
                    console.log(`接机订单: 自动设置上车地址为机场: ${airport}`);
                }

                if (warnings.length > 0) {
                    console.warn("数据验证/清理警告:", warnings);
                }

                // 如果核心字段缺失，即使有其他数据，也返回 null
                if (!isPotentiallyValid) {
                    console.error("数据经验证后核心字段缺失或严重错误，判定为无效");
                    return null;
                }

                // 确保备注字段始终为固定值
                sanitizedData.remark = "要用 携程司导端 工作。 this job need use china app";

                return sanitizedData; // 返回清理后的数据
            },

            // *** 新增：计算送机上车时间 (-3.5小时) ***
            calculatePickupTimeForDropoff: function(departureTimeStr, departureDateStr) {
                if (!departureTimeStr || !departureDateStr) return null; // 需要日期和时间

                const timeMatch = departureTimeStr.match(/^(\d{2}):(\d{2})$/);
                const dateMatch = departureDateStr.match(/^(\d{2})-(\d{2})-(\d{4})$/); // DD-MM-YYYY

                if (!timeMatch || !dateMatch) {
                    console.warn("送机时间计算失败：无效的日期或时间格式", departureDateStr, departureTimeStr);
                    return null;
                }

                const day = parseInt(dateMatch[1], 10);
                const month = parseInt(dateMatch[2], 10) - 1; // JS 月份从0开始
                const year = parseInt(dateMatch[3], 10); // 直接使用传入日期的年份
                const hour = parseInt(timeMatch[1], 10);
                const minute = parseInt(timeMatch[2], 10);

                // 创建起飞时间 Date 对象 (UTC是为了避免本地时区问题，但计算差值没关系)
                const departureDateTime = new Date(Date.UTC(year, month, day, hour, minute));

                // 减去 3.5 小时 (即 210 分钟)
                departureDateTime.setUTCMinutes(departureDateTime.getUTCMinutes() - 210);

                // 格式化为 DD-MM-YYYY HH:MM
                const pickupDay = departureDateTime.getUTCDate().toString().padStart(2, '0');
                const pickupMonth = (departureDateTime.getUTCMonth() + 1).toString().padStart(2, '0'); // 转回 1-12 月
                const pickupYear = departureDateTime.getUTCFullYear(); // 使用计算后的年份，可能因减去时间跨天导致变化
                const pickupHour = departureDateTime.getUTCHours().toString().padStart(2, '0');
                const pickupMinute = departureDateTime.getUTCMinutes().toString().padStart(2, '0');

                const result = `${pickupDay}-${pickupMonth}-${pickupYear} ${pickupHour}:${pickupMinute}`;
                console.log(`送机时间计算: ${departureDateStr} ${departureTimeStr} - 3.5h = ${result}`);
                return result;
            },
            // *** 时间计算辅助函数结束 ***

        };

        // 5. 订单处理逻辑
        const orderProcessing = {
            // 分割多个订单
            splitOrders: function(rawOrderText) {
                const orders = [];
                const orderRegex = /订单编号：\d{19}/g;
                let matches = [...rawOrderText.matchAll(orderRegex)];
                
                if (matches.length <= 1) {
                    // 如果只有一个订单编号或没有订单编号，返回整个文本
                    return [rawOrderText];
                }
                
                // 有多个订单编号，按照订单编号分割
                for (let i = 0; i < matches.length; i++) {
                    const currentMatch = matches[i];
                    const currentIndex = currentMatch.index;
                    
                    // 如果是最后一个匹配，截取到文本结尾
                    if (i === matches.length - 1) {
                        orders.push(rawOrderText.substring(currentIndex));
                    } else {
                        // 否则截取到下一个匹配的位置
                        const nextMatch = matches[i + 1];
                        const nextIndex = nextMatch.index;
                        orders.push(rawOrderText.substring(currentIndex, nextIndex));
                    }
                }
                
                return orders;
            },
            
            // 转换订单主函数
            convertOrder: async function() {
                // 获取原始订单文本
                const rawOrderText = dom.elements.rawOrderTextarea.value.trim();
                
                if (!rawOrderText) {
                    dom.showNotification(i18n.getString('please-enter-data'));
                    return;
                }
                
                // 显示加载中提示
                dom.showNotification('正在处理订单数据，请稍等...');
                
                try {
                    // 检查是否有多个订单
                    const orders = this.splitOrders(rawOrderText);
                    
                    if (orders.length > 1) {
                        // 多订单处理
                        await this.processMultipleOrders(rawOrderText);
                    } else {
                        // 单订单处理
                        await this.processSingleOrder(rawOrderText);
                    }
                    
                    // 处理完成后显示成功提示
                    dom.showNotification('订单处理完成！');
                } catch (error) {
                    console.error('订单处理错误:', error);
                    dom.showNotification('订单处理出错: ' + error.message);
                }
            },
            
            // 处理单个订单
            processSingleOrder: async function(rawOrderText) {
                dom.showElement(dom.elements.gridContainer);
                dom.hideElement(dom.elements.multiOrdersContainer);
                
                try {
                    // 使用新的本地处理逻辑处理订单
                    const orderResults = await OrderProcessor.processWithAPI(rawOrderText);
                    console.log('订单处理结果:', orderResults);
                    
                    if (!orderResults || orderResults.length === 0) {
                        console.warn("没有可处理的订单数据");
                        dom.showNotification("未能识别有效的订单信息", "warning");
                        this.resetForm();
                        return;
                    }
                    
                    // 处理处理结果
                    if (orderResults.length > 1) {
                        console.log('检测到多个订单（有附加服务），切换到多订单显示模式');
                        dom.elements.ordersList.innerHTML = '';
                        
                        for (let i = 0; i < orderResults.length; i++) {
                            const order = this.applyDefaultsIfNeeded(orderResults[i]);
                            if (order) {
                                const orderElement = this.createOrderElement(order, rawOrderText, i + 1);
                                dom.elements.ordersList.appendChild(orderElement);
                            }
                        }
                        
                        dom.hideElement(dom.elements.gridContainer);
                        dom.showElement(dom.elements.multiOrdersContainer);
                        appState.multiOrderViewActive = true;
                        dom.showNotification('检测到多个订单（含附加服务），已全部处理', 'success');
                    } else {
                        console.log('处理单个订单');
                        const singleOrder = this.applyDefaultsIfNeeded(orderResults[0]);
                        
                        if (singleOrder) {
                            await this.fillOrderForm(singleOrder);
                            dom.showElement(dom.elements.gridContainer);
                            dom.hideElement(dom.elements.multiOrdersContainer);
                            appState.multiOrderViewActive = false;
                            dom.showNotification('订单处理成功', 'success');
                        } else {
                            console.error("无法处理订单数据");
                            dom.showNotification("处理订单数据时发生错误", "error");
                            this.resetForm();
                        }
                    }
                } catch (error) {
                    console.error('订单处理错误:', error);
                    dom.showNotification('处理订单时出错: ' + error.message, 'error');
                    this.resetForm();
                }
            },
            
            // 验证订单数据是否包含所有必要字段
            validateOrderData: function(data) {
                if (!data) return false;
                
                // 检查必填字段
                const requiredFields = ['name', 'pickup-address', 'dropoff-address', 'pickup-datetime'];
                for (const field of requiredFields) {
                    if (!data[field] || data[field].trim() === '') {
                        console.log(`订单数据缺少必填字段: ${field}`);
                        return false;
                    }
                }
                
                // 如果是接送机订单，检查航班号
                if (data.category === 'airport' && (!data['flight-number'] || data['flight-number'].trim() === '')) {
                    console.log('接送机订单缺少航班号');
                    return false;
                }
                
                return true;
            },
            
            // 合并两个订单数据对象，优先使用第一个对象的数据
            mergeOrderData: function(primary, secondary) {
                if (!primary) return secondary;
                if (!secondary) return primary;
                
                const result = {...secondary};
                
                // 遍历primary对象的所有属性
                for (const key in primary) {
                    // 只有当primary中的属性值不为空时才使用它
                    if (primary[key] && primary[key].toString().trim() !== '') {
                        result[key] = primary[key];
                    }
                }
                
                return result;
            },
            
            // 处理多个订单
            processMultipleOrders: async function(rawOrderText) {
                dom.hideElement(dom.elements.gridContainer);
                dom.showElement(dom.elements.multiOrdersContainer);
                dom.elements.ordersList.innerHTML = '';
                let allOrderResults = [];
                
                try {
                    // 拆分原始文本
                    const orders = OrderProcessor.splitOrders(rawOrderText);
                    console.log(`拆分得到 ${orders.length} 个潜在订单`);
                    
                    // 处理每个拆分的订单
                    for (let i = 0; i < orders.length; i++) {
                        const orderText = orders[i];
                        try {
                            // 使用本地提取处理单个订单
                            const processedOrders = await OrderProcessor.processWithAPI(orderText);
                            
                            // processWithAPI现在返回数组，可能包含主订单和附加服务订单
                            if (Array.isArray(processedOrders) && processedOrders.length > 0) {
                                // 将处理后的所有订单添加到结果数组
                                processedOrders.forEach(order => {
                                    allOrderResults.push(order);
                                });
                            }
                        } catch (extractError) {
                            console.error(`处理订单 ${i + 1} 时出错:`, extractError);
                            // 继续处理其他订单
                        }
                    }
                    
                    // 检查是否成功处理了任何订单
                    if (allOrderResults.length === 0) {
                        dom.showNotification('未能识别任何有效订单', 'error');
                        return;
                    }
                    
                    // 处理和显示所有订单
                    console.log(`本地处理得到 ${allOrderResults.length} 个订单`);
                    
                    // 创建订单元素并显示
                    for (let i = 0; i < allOrderResults.length; i++) {
                        const orderWithDefaults = this.applyDefaultsIfNeeded(allOrderResults[i]);
                        console.log(`订单 #${i+1} 应用默认值后的数据:`, orderWithDefaults);
                        
                        if (orderWithDefaults) {
                            const orderElement = this.createOrderElement(orderWithDefaults, rawOrderText, i + 1);
                            dom.elements.ordersList.appendChild(orderElement);
                        }
                    }
                    
                    dom.showNotification('已成功处理多个订单', 'success');
                    appState.multiOrderViewActive = true;
                } catch (error) {
                    console.error('处理多个订单时出错:', error);
                    dom.showNotification('处理订单时出错: ' + error.message, 'error');
                }
            },

        // 填充订单表单
            fillOrderForm: async function(orderData) {
                console.log('填充订单表单:', orderData);
                
                if (!orderData) {
                    dom.showNotification('未能获取有效的订单数据', 'warning');
                    return;
                }
                
                try {
            // 设置OTA平台 - 优先使用orderData中的ota字段，如果没有则默认为携程专车
                    dom.elements.otaInput.value = orderData.ota || '携程专车';
            
            // 车型字段优先使用vehicle-type，其次car-type
            const carTypeValue = orderData['vehicle-type'] || orderData['car-type'] || '';
                    dom.elements.carTypeInput.value = carTypeValue;
                    
                    // 乘客姓名 - 必须设置
                    dom.elements.nameInput.value = orderData.name || '';
                    
                    // 订单编号处理
                    dom.elements.otaReferenceInput.value = orderData['ota-reference'] || '';
                    
                    // 价格处理
                    dom.elements.priceInput.value = orderData.price || '1';
                    
                    // 电话处理
                    dom.elements.phoneInput.value = orderData.phone || '';
                    
                    // 电子邮件处理
                    dom.elements.emailInput.value = orderData.email || '';
                    
                    // 航班信息处理
                    dom.elements.flightNumberInput.value = orderData['flight-number'] || '';
                    
                    // 接送时间处理
                    dom.elements.pickupDatetimeInput.value = orderData['pickup-datetime'] || '';
                    
                    // 上下车地址处理
                    dom.elements.pickupAddressInput.value = orderData['pickup-address'] || '';
                    dom.elements.dropoffAddressInput.value = orderData['dropoff-address'] || '';
                    
                    // 乘客人数处理
                    dom.elements.passengerNumberInput.value = orderData['passenger-number'] || '1';
                    
                    // 语言处理
                    dom.elements.languageInput.value = orderData.language || 'chinese';
                    
                    // 订单类型处理
                    dom.elements.categorySelect.value = orderData.category || 'airport';
                    dom.elements.subcategorySelect.value = orderData.subcategory || 'pickup';
                    
                    // 驾驶区域处理
                    dom.elements.drivingRegionSelect.value = orderData['driving-region'] || 'kl';
                    
                    // 当类型为接机且上车地址为空时，自动设置为机场
                    if ((orderData.category === 'airport' || dom.elements.categorySelect.value === 'airport') && 
                        (orderData.subcategory === 'pickup' || dom.elements.subcategorySelect.value === 'pickup') && 
                        (!orderData['pickup-address'] || orderData['pickup-address'].trim() === '')) {
                        // 根据驾驶区域选择机场
                        const region = orderData['driving-region'] || dom.elements.drivingRegionSelect.value || 'kl';
                        let airport = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                        if (region === 'sg') {
                            airport = 'Singapore Changi Airport';
                        } else if (region === 'kk') {
                            airport = 'Kota Kinabalu International Airport';
                        }
                        dom.elements.pickupAddressInput.value = airport;
                        console.log(`接机订单: 表单填充时自动设置上车地址为 ${airport}`);
                    }
                    
                    // 司机数量处理
                    dom.elements.driverCountInput.value = orderData.driver || '1';
                    
                    // 备注处理 - 始终使用固定值
                    dom.elements.remarkTextarea.value = "要用 携程司导端 工作。 this job need use china app";
                    
                    // 检查缺失的必填字段
                    const missingFields = [];
                    
                    if (!orderData.name) missingFields.push('乘客姓名');
                    if (!orderData['pickup-address']) missingFields.push('接机地址');
                    if (!orderData['dropoff-address']) missingFields.push('送机地址');
                    if (!orderData['pickup-datetime']) missingFields.push('接送时间');
                    
                    // 如果有缺失字段，显示警告
                    if (missingFields.length > 0) {
                        dom.showNotification(`[系统提示] 以下字段缺失: ${missingFields.join(', ')}`, 'warning');
                    }
                    
                    // 触发表单变化事件，以便于任何可能的依赖更新
                    document.querySelectorAll('input, select, textarea').forEach(element => {
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    });
                    
                    // 禁用编辑状态
                    this.disableFormEditing();
                    
                } catch (error) {
                    console.error('填充订单表单时出错:', error);
                    dom.showNotification('填充订单表单时出错: ' + error.message);
                }
            },
            
            // 创建订单元素 (显示更详细信息)
            createOrderElement: function(orderData, rawText, index) {
                const orderDiv = document.createElement('div');
                orderDiv.className = 'order-item'; // 使用与之前相同的基类
                
                // 添加移动端适配类
                if (window.matchMedia('(max-width: 767px)').matches) {
                    orderDiv.classList.add('order-item-mobile');
                }

                // 根据订单类型添加不同的背景色
                if (orderData.category === 'airport') {
                    if (orderData.subcategory === 'pickup') {
                        orderDiv.style.borderLeft = '4px solid #4CAF50'; // 接机绿色
                    } else if (orderData.subcategory === 'dropoff') {
                        orderDiv.style.borderLeft = '4px solid #2196F3'; // 送机蓝色
                    }
                } else {
                    orderDiv.style.borderLeft = '4px solid #FF9800'; // 包车橙色
                }
                
                // 添加阴影效果提升可视化
                orderDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                orderDiv.style.borderRadius = '4px';
                orderDiv.style.padding = '12px 15px';
                orderDiv.style.marginBottom = '15px';

                // --- 订单标题 ---
                const header = document.createElement('div');
                header.className = 'order-header';
                header.style.display = 'flex';
                header.style.alignItems = 'center';
                header.style.justifyContent = 'space-between';
                header.style.marginBottom = '10px';
                
                const titleGroup = document.createElement('div');
                
                const title = document.createElement('h3');
                // 尝试使用更有意义的标题，如包含姓名和日期
                const titleText = orderData.name && orderData['pickup-datetime']
                    ? `${orderData.name} - ${orderData['pickup-datetime'].split(' ')[0]}` // 只取日期部分
                    : `${i18n.getString('order-number')} #${index}`;
                title.textContent = titleText;
                title.style.fontSize = '1.1rem'; // 稍微调整标题大小
                title.style.fontWeight = '600';
                title.style.margin = '0 0 5px 0';
                
                // 添加订单类型标签
                const typeLabel = document.createElement('span');
                typeLabel.textContent = this.getOrderTypeLabel(orderData);
                typeLabel.style.fontSize = '0.8rem';
                typeLabel.style.padding = '2px 8px';
                typeLabel.style.borderRadius = '20px';
                typeLabel.style.marginLeft = '10px';
                typeLabel.style.fontWeight = '500';
                
                // 根据订单类型设置标签颜色
                if (orderData.category === 'airport') {
                    if (orderData.subcategory === 'pickup') {
                        typeLabel.style.backgroundColor = '#E8F5E9';
                        typeLabel.style.color = '#2E7D32';
                    } else if (orderData.subcategory === 'dropoff') {
                        typeLabel.style.backgroundColor = '#E3F2FD';
                        typeLabel.style.color = '#1565C0';
                    }
                } else {
                    typeLabel.style.backgroundColor = '#FFF3E0';
                    typeLabel.style.color = '#E65100';
                }
                
                // 添加订单类型和子类型标签（显示实际值）
                const categoryInfo = document.createElement('span');
                
                // 获取类型和子类型的中文表示
                const categoryText = orderData.category === 'airport' ? '机场服务' : 
                                    orderData.category === 'charter' ? '包车服务' : 
                                    orderData.category || '';
                                    
                const subcategoryText = orderData.subcategory === 'pickup' ? '接机' : 
                                      orderData.subcategory === 'dropoff' ? '送机' : 
                                      orderData.subcategory === 'charter' ? '包车' : 
                                      orderData.subcategory || '';
                
                categoryInfo.textContent = `${categoryText}/${subcategoryText}`;
                categoryInfo.style.fontSize = '0.75rem';
                categoryInfo.style.padding = '2px 8px';
                categoryInfo.style.borderRadius = '20px';
                categoryInfo.style.backgroundColor = '#ECEFF1';
                categoryInfo.style.color = '#455A64';
                categoryInfo.style.marginLeft = '8px';
                categoryInfo.style.fontWeight = '500';
                
                // 添加车型标签
                const vehicleLabel = document.createElement('span');
                const vehicleType = orderData['vehicle-type'] || '';
                vehicleLabel.textContent = vehicleType === 'sedan' ? '五座' : 
                                          vehicleType === 'mpv' ? '七座' : 
                                          vehicleType || '';
                vehicleLabel.style.fontSize = '0.8rem';
                vehicleLabel.style.padding = '2px 8px';
                vehicleLabel.style.borderRadius = '20px';
                vehicleLabel.style.backgroundColor = '#F3E5F5';
                vehicleLabel.style.color = '#6A1B9A';
                vehicleLabel.style.marginLeft = '8px';
                vehicleLabel.style.fontWeight = '500';
                
                titleGroup.appendChild(title);
                
                // 添加订单子标题（时间和航班号）
                if (orderData['pickup-datetime']) {
                    const subtitle = document.createElement('div');
                    subtitle.style.fontSize = '0.9rem';
                    subtitle.style.color = '#555';
                    subtitle.style.marginTop = '3px';
                    
                    // 格式化时间显示
                    const [date, time] = orderData['pickup-datetime'].split(' ');
                    subtitle.textContent = `⏱️ ${time}`;
                    
                    // 如果有航班号，添加到副标题
                    if (orderData['flight-number']) {
                        subtitle.textContent += ` | ✈️ ${orderData['flight-number']}`;
                    }
                    
                    titleGroup.appendChild(subtitle);
                }
                
                const labelGroup = document.createElement('div');
                labelGroup.style.display = 'flex';
                labelGroup.style.marginTop = '5px';
                labelGroup.appendChild(typeLabel);
                labelGroup.appendChild(categoryInfo);
                labelGroup.appendChild(vehicleLabel);
                titleGroup.appendChild(labelGroup);

                // --- 按钮组 ---
                const buttonGroup = document.createElement('div');
                buttonGroup.style.display = 'flex';
                buttonGroup.style.gap = '0.5rem';
                buttonGroup.style.alignItems = 'flex-start';

                // 复制按钮
                const copyButton = document.createElement('button');
                copyButton.className = 'btn btn-gray btn-sm'; // 使用稍小尺寸的按钮
                copyButton.style.padding = '0.25rem 0.6rem';
                copyButton.style.fontSize = '0.8rem';
                copyButton.textContent = i18n.getString('copy');
                copyButton.style.borderRadius = '4px';
                copyButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    clipboard.copyOrderData(orderData); // 使用现有的复制逻辑
                });

                // 编辑此订单按钮
                const editThisOrderButton = document.createElement('button');
                editThisOrderButton.className = 'btn btn-primary btn-sm'; // 使用稍小尺寸的按钮
                editThisOrderButton.style.padding = '0.25rem 0.6rem';
                editThisOrderButton.style.fontSize = '0.8rem';
                editThisOrderButton.style.borderRadius = '4px';
                editThisOrderButton.textContent = i18n.getString('enable-edit'); // "编辑"
                editThisOrderButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log(`编辑订单 #${index}:`, orderData);
                    appState.multiOrderViewActive = true; // **新增：标记来自多订单视图**
                    this.fillOrderForm(orderData);
                    dom.showElement(dom.elements.gridContainer);
                    dom.hideElement(dom.elements.multiOrdersContainer);
                    this.enableFormEditing(); // 直接启用编辑
                    dom.showNotification(i18n.getString('edit-enabled'), 'info'); // 用 info 类型
                });

                buttonGroup.appendChild(copyButton);
                buttonGroup.appendChild(editThisOrderButton);
            
                header.appendChild(titleGroup);
                header.appendChild(buttonGroup); // 将按钮组加入标题行
                orderDiv.appendChild(header);
            
                // --- 订单详情 (显示所有字段) ---
                const details = document.createElement('div');
                details.className = 'order-content';
                details.style.marginTop = '0.75rem'; // 添加一些间距
                details.style.fontSize = '0.85rem'; // 稍微缩小字体
                
                // 创建两列布局
                details.style.display = 'grid';
                details.style.gridTemplateColumns = window.matchMedia('(max-width: 767px)').matches ? '1fr' : '1fr 1fr';
                details.style.gridGap = '10px';

                // 使用 clipboard.copyOrderData 中的字段映射来获取标签
                const fieldMapping = {
                     'ota': 'OTA平台',
                     'ota-reference': 'OTA订单号',
                     'price': '价格',
                     'name': '乘客姓名',
                     'phone': '电话',
                     'email': '邮箱',
                     'flight-number': '航班号',
                     'pickup-datetime': '上车时间', // 改为更通用的标签
                     'pickup-address': '上车地址',
                     'dropoff-address': '下车地址',
                     'vehicle-type': '车型',
                     'luggage-number': '行李数', // 更简洁
                     'passenger-number': '乘客数', // 更简洁
                     'language': '语言',
                     'category': '类别',
                     'subcategory': '子类别',
                     'driving-region': '驾驶区域',
                     'driver-count': '司机数', // 更简洁
                     'remark': '备注'
                };

                // 定义期望的显示顺序和分组
                const leftColumnFields = [
                    'name', 'phone', 'email',
                    'pickup-datetime', 'flight-number',
                    'vehicle-type', 'passenger-number', 'luggage-number'
                ];
                
                const rightColumnFields = [
                    'pickup-address', 'dropoff-address',
                    'driving-region', 'price', 'ota', 'ota-reference'
                ];
                
                // 左列：关键人员和时间信息
                const leftColumn = document.createElement('div');
                leftColumn.style.display = 'flex';
                leftColumn.style.flexDirection = 'column';
                leftColumn.style.gap = '8px';
                
                for (const key of leftColumnFields) {
                    if (orderData.hasOwnProperty(key) && orderData[key] && orderData[key].toString().trim() !== '') {
                        const fieldDiv = this.createInfoField(key, orderData[key], fieldMapping);
                        leftColumn.appendChild(fieldDiv);
                    }
                }
                
                // 右列：地址和其他信息
                const rightColumn = document.createElement('div');
                rightColumn.style.display = 'flex';
                rightColumn.style.flexDirection = 'column';
                rightColumn.style.gap = '8px';
                
                for (const key of rightColumnFields) {
                    if (orderData.hasOwnProperty(key) && orderData[key] && orderData[key].toString().trim() !== '') {
                        const fieldDiv = this.createInfoField(key, orderData[key], fieldMapping);
                        rightColumn.appendChild(fieldDiv);
                    }
                }
                
                // 备注单独处理，跨列显示
                if (orderData.remark && orderData.remark.trim() !== '') {
                    const remarkDiv = document.createElement('div');
                    remarkDiv.style.gridColumn = '1 / -1'; // 跨所有列
                    remarkDiv.style.marginTop = '10px';
                    remarkDiv.style.padding = '8px';
                    remarkDiv.style.backgroundColor = '#FFF8E1';
                    remarkDiv.style.borderRadius = '4px';
                    
                    const remarkLabel = document.createElement('strong');
                    remarkLabel.textContent = '备注: ';
                    remarkLabel.style.color = '#FF8F00';
                    
                    const remarkContent = document.createElement('span');
                    remarkContent.textContent = orderData.remark;
                    
                    remarkDiv.appendChild(remarkLabel);
                    remarkDiv.appendChild(remarkContent);
                    details.appendChild(remarkDiv);
                }
                
                details.appendChild(leftColumn);
                details.appendChild(rightColumn);

                // 如果没有任何字段被添加（理论上不应该发生），加个提示
                if (leftColumn.children.length === 0 && rightColumn.children.length === 0 && !orderData.remark) {
                    const noDataDiv = document.createElement('p');
                    noDataDiv.textContent = '无详细信息可显示。';
                    noDataDiv.style.fontStyle = 'italic';
                    noDataDiv.style.gridColumn = '1 / -1';
                    details.appendChild(noDataDiv);
                }

                orderDiv.appendChild(details);
                return orderDiv;
            },

            // 新增：创建信息字段的辅助函数
            createInfoField: function(key, value, fieldMapping) {
                const fieldDiv = document.createElement('div');
                fieldDiv.style.display = 'flex';
                
                // 特殊处理某些字段的显示值
                let displayValue = value;
                if (key === 'driving-region') {
                    displayValue = this.getDrivingRegionLabel(value);
                } else if (key === 'category' && value === 'airport') {
                    // 修复：不使用orderData变量，因为它不在此函数作用域中
                    // 类别信息不完整时，简化显示
                    displayValue = '机场服务';
                } else if (key === 'subcategory') {
                    return document.createElement('div'); // 跳过单独显示子类别
                } else if (key === 'vehicle-type') {
                    // 美化车型显示
                    if (value === 'sedan') {
                        displayValue = '五座 (Sedan)';
                    } else if (value === 'mpv') {
                        displayValue = '七座 (MPV)';
                    }
                }
                
                // 应用图标和样式
                let icon = '';
                let labelColor = '#555';
                
                switch (key) {
                    case 'name':
                        icon = '👤 '; break;
                    case 'phone':
                        icon = '📞 '; break;
                    case 'email':
                        icon = '📧 '; break;
                    case 'pickup-datetime':
                        icon = '⏱️ '; break;
                    case 'flight-number':
                        icon = '✈️ '; break;
                    case 'pickup-address':
                        icon = '🏁 '; break;
                    case 'dropoff-address':
                        icon = '🏢 '; break;
                    case 'vehicle-type':
                        icon = '🚗 '; break;
                    case 'passenger-number':
                        icon = '👥 '; break;
                    case 'luggage-number':
                        icon = '🧳 '; break;
                    case 'price':
                        icon = '💰 '; 
                        labelColor = '#D32F2F';
                        break;
                    case 'driving-region':
                        icon = '🗺️ '; break;
                    case 'ota':
                    case 'ota-reference':
                        icon = '🔖 '; break;
                }
                
                const label = document.createElement('div');
                label.style.minWidth = '90px';
                label.style.fontWeight = '600';
                label.style.color = labelColor;
                label.textContent = icon + (fieldMapping[key] || key) + ':';
                
                const content = document.createElement('div');
                content.style.flex = '1';
                content.textContent = displayValue;
                
                // 为地址添加可折叠处理
                if ((key === 'pickup-address' || key === 'dropoff-address') && displayValue.length > 40) {
                    content.textContent = displayValue.substring(0, 40) + '...';
                    content.title = displayValue; // 鼠标悬停显示完整地址
                    content.style.cursor = 'pointer';
                    
                    // 点击切换显示完整/折叠地址
                    content.addEventListener('click', function() {
                        if (this.dataset.expanded === 'true') {
                            this.textContent = displayValue.substring(0, 40) + '...';
                            this.dataset.expanded = 'false';
                        } else {
                            this.textContent = displayValue;
                            this.dataset.expanded = 'true';
                        }
                    });
                    content.dataset.expanded = 'false';
                }
                
                fieldDiv.appendChild(label);
                fieldDiv.appendChild(content);
                
                return fieldDiv;
            },

            // 获取订单类型标签
            getOrderTypeLabel: function(orderData) {
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                        return i18n.getString('airport-pickup');
                } else if (orderData.subcategory === 'dropoff') {
                        return i18n.getString('airport-dropoff');
                }
            }
                return i18n.getString('charter');
            },
        
        // 获取驾驶区域标签
            getDrivingRegionLabel: function(regionCode) {
            const regionMap = {
                'kl': '吉隆坡',
                'penang': '槟城',
                'sg': '新加坡',
                'jb': '新山',
                'sabah': '沙巴'
            };
            
            if (!regionCode) return '';
            
            // 尝试小写匹配
            const lowerCode = regionCode.toLowerCase();
            if (regionMap[lowerCode]) {
                return regionMap[lowerCode];
            }
            
            // 如果没有匹配到，返回原始代码
            return regionCode.toUpperCase();
            },
            
            // 启用表单字段编辑
            enableFormEditing: function() {
                dom.elements.formInputs.forEach(input => {
                    input.readOnly = false;
                    input.disabled = false;
                });
                dom.hideElement(dom.elements.editBtn);
                dom.showElement(dom.elements.saveBtn);
                dom.showElement(dom.elements.cancelEditBtn); // 显示取消按钮
                appState.isEditing = true;
                // 可选：添加高亮样式到卡片
                dom.elements.saveBtn.closest('.card').classList.add('editing');
            },
            
            // 禁用表单字段编辑
            disableFormEditing: function() {
                dom.elements.formInputs.forEach(input => {
                    input.readOnly = true;
                    input.disabled = true;
                });
                dom.hideElement(dom.elements.saveBtn);
                dom.hideElement(dom.elements.cancelEditBtn); // 隐藏取消按钮
                dom.showElement(dom.elements.editBtn);
                appState.isEditing = false;
                // 可选：移除高亮样式
                dom.elements.editBtn.closest('.card').classList.remove('editing');
            },
            
            // 重置表单
            resetForm: function() {
                dom.elements.rawOrderTextarea.value = '';
                // 可以添加清空其他表单字段的代码
            },

            // *** 确保 applyDefaultsIfNeeded 在这里定义 ***
             applyDefaultsIfNeeded: function(orderData) {
                 if (!orderData) return {}; // 返回空对象避免后续错误

                 // 复制一份，避免修改原始数据
                 const result = { ...orderData };

                 const defaults = {
                     price: "1",
                     'passenger-number': "1",
                     'luggage-number': "0",
                     remark: "要用 携程司导端 工作。 this job need use china app",
                     'driver-count': 1,
                     language: "chinese",
                     ota: "携程专车",
                     // 以下字段通常应该被提取，但也提供基础默认值以防万一
                     name: "",
                     'pickup-address': "",
                     'dropoff-address': "",
                     'pickup-datetime': "",
                     'vehicle-type': "sedan",
                     category: "airport",
                     subcategory: "pickup",
                     'driving-region': "kl",
                     email: "",
                     'flight-number': "",
                     phone: "",
                     'ota-reference': ""
                 };

                 // 遍历所有可能的字段，如果传入的数据中没有或者为空，则应用默认值
                 for (const key in defaults) {
                     if (!result.hasOwnProperty(key) || result[key] === null || result[key] === undefined || result[key].toString().trim() === '') {
                         // 特殊处理需要计算的默认值：phone 和 ota-reference
                         if ((key === 'phone' || key === 'ota-reference')) {
                             if (result.name && result['pickup-datetime']) {
                                 const dateMatch = result['pickup-datetime'].match(/^(\d{2})-(\d{2})/); // DD-MM
                                 if (dateMatch) {
                                     result[key] = `${result.name}${dateMatch[2]}${dateMatch[1]}`; // 名字 + MMDD
                                 } else {
                                     // 如果日期格式不对，提供一个更通用的默认值
                                     result[key] = `${result.name || '未知'}默认值`;
                                 }
                             } else {
                                 // 如果缺少名字或日期，无法生成此默认值
                                 result[key] = defaults[key]; // 使用上面定义的空字符串 ""
                             }
                         } else {
                             // 应用其他普通默认值
                             result[key] = defaults[key];
                         }
                     }
                 }

                 // 确保数字字段最终是字符串类型，以便填充表单 input[type=number]
                 ['passenger-number', 'luggage-number', 'driver-count'].forEach(key => {
                     if (result[key] !== undefined && result[key] !== null) {
                         result[key] = result[key].toString();
                     }
                 });
                  // 确保价格是字符串
                  if (result.price !== undefined && result.price !== null) {
                       result.price = result.price.toString();
                   }

                 // 标准化车型字段
                 if (result['vehicle-type']) {
                     const vehicleType = result['vehicle-type'].toLowerCase();
                     // 五座车型标准化为sedan
                     if (vehicleType.includes('五座') || vehicleType === 'sedan' || vehicleType === '5座' || vehicleType === '5人座') {
                         result['vehicle-type'] = 'sedan';
                     } 
                     // 七座车型标准化为mpv
                     else if (vehicleType.includes('七座') || vehicleType === '7座' || vehicleType === '7人座' || 
                              vehicleType === 'suv' || vehicleType === 'serena' || vehicleType === 'starex' || 
                              vehicleType === 'alphard' || vehicleType === 'mpv') {
                         result['vehicle-type'] = 'mpv';
                     }
                 }

                 // 确保接机订单上车地址为机场，下车地址为酒店
                 if (result.category === 'airport' && result.subcategory === 'pickup') {
                     // 检查上车地址是否为空或不含有机场关键词
                     const airportRegex = /(?:KLIA|机场|Airport|Terminal)/i;
                     // 机场地址处理
                     if (!result['pickup-address'] || !airportRegex.test(result['pickup-address'])) {
                         // 根据驾驶区域设置对应机场
                         if (result['driving-region'] === 'sg') {
                             result['pickup-address'] = 'Singapore Changi Airport';
                         } else if (result['driving-region'] === 'kk') {
                             result['pickup-address'] = 'Kota Kinabalu International Airport';
                         } else {
                             result['pickup-address'] = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                         }
                         console.log(`接机订单: 应用默认值时设置上车地址为机场: ${result['pickup-address']}`);
                     }
                     
                     // 检查接机订单的下车地址是否错误地包含机场关键词
                     if (result['dropoff-address'] && airportRegex.test(result['dropoff-address']) && 
                         result['pickup-address'] !== result['dropoff-address']) {
                         // 如果下车地址是机场且与上车地址不同，可能是地址颠倒了
                         // 将下车地址调整为酒店信息或留空
                         console.log(`接机订单: 下车地址不应为机场，需要修正`);
                         // 如果有酒店信息，就用酒店信息
                         if (result.name && result.name.includes('Hotel')) {
                             result['dropoff-address'] = result.name;
                         }
                     }
                 }

                 // 确保送机订单上车地址为酒店，下车地址为机场
                 if (result.category === 'airport' && result.subcategory === 'dropoff') {
                     // 检查下车地址是否为空或不含有机场关键词
                     const airportRegex = /(?:KLIA|机场|Airport|Terminal)/i;
                     // 机场地址处理
                     if (!result['dropoff-address'] || !airportRegex.test(result['dropoff-address'])) {
                         // 根据驾驶区域设置对应机场
                         if (result['driving-region'] === 'sg') {
                             result['dropoff-address'] = 'Singapore Changi Airport';
                         } else if (result['driving-region'] === 'kk') {
                             result['dropoff-address'] = 'Kota Kinabalu International Airport';
                         } else {
                             result['dropoff-address'] = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                         }
                         console.log(`送机订单: 应用默认值时设置下车地址为机场: ${result['dropoff-address']}`);
                     }
                     
                     // 检查送机订单的上车地址是否错误地包含机场关键词
                     if (result['pickup-address'] && airportRegex.test(result['pickup-address']) && 
                         result['pickup-address'] !== result['dropoff-address']) {
                         // 如果上车地址是机场且与下车地址不同，可能是地址颠倒了
                         // 尝试找到酒店信息
                         console.log(`送机订单: 上车地址不应为机场，需要修正`);
                         // 如果有酒店信息，就用酒店信息
                         if (result.name && result.name.includes('Hotel')) {
                             result['pickup-address'] = result.name;
                         }
                     }
                 }

                 // 确保remark字段始终为固定值，不受原始数据或其他处理的影响
                 result.remark = "要用 携程司导端 工作。 this job need use china app";

                 console.log("应用默认值后的数据:", result);
                 return result;
            },

            // ... (validateOrderData, mergeOrderData, fillOrderForm, createOrderElement 等) ...
            // ... (enable/disable/resetForm) ...
        };

        // 6. 剪贴板操作
        const clipboard = {
            // 复制到剪贴板
            copyToClipboard: function(text) {
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    // 优先使用现代剪贴板API
                    navigator.clipboard.writeText(text)
                        .then(() => {
                            dom.showNotification(i18n.getString('data-copied'));
                        })
                        .catch(err => {
                            console.error('剪贴板API失败，使用回退方法:', err);
                            this.copyToClipboardFallback(text);
                        });
                } else {
                    // 回退方法
                    this.copyToClipboardFallback(text);
                }
            },
            
            // 剪贴板复制回退方法
            copyToClipboardFallback: function(text) {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                textarea.style.opacity = '0';
                document.body.appendChild(textarea);
                textarea.select();
                
                try {
                    const success = document.execCommand('copy');
                    if (success) {
                        dom.showNotification(i18n.getString('data-copied'));
                    } else {
                        console.error('execCommand复制失败');
                        dom.showNotification('复制失败', 'danger');
                    }
                } catch (err) {
                    console.error('execCommand复制出错:', err);
                    dom.showNotification('复制失败', 'danger');
                } finally {
                    document.body.removeChild(textarea);
                }
            },

        // 复制表单数据
            copyFormData: function() {
            let formattedData = '';
            
                dom.elements.formInputs.forEach(input => {
                if (input.id && input.value) {
                    const label = input.previousElementSibling?.textContent || input.id;
                    formattedData += `${label}: ${input.value}\n`;
                }
            });
            
                this.copyToClipboard(formattedData);
            },

        // 复制订单数据
            copyOrderData: function(orderData) {
            // 创建一个副本以避免修改原始数据
            const dataToFormat = { ...orderData };
            
            // 确保关键字段存在
            if (!dataToFormat.category) dataToFormat.category = 'airport';
            if (!dataToFormat.subcategory) dataToFormat.subcategory = 'pickup';
            if (!dataToFormat['driver-count']) dataToFormat['driver-count'] = '1';
            
            // 获取订单类型标签
            let orderTypeDisplay = orderProcessing.getOrderTypeLabel(dataToFormat);
            
            // 继续后续的格式化和复制操作...
            let formattedData = '';
            
            // 字段映射
            const fieldMapping = {
                'ota': 'OTA平台',
                'ota-reference': 'OTA订单号',
                'price': '价格',
                'name': '乘客姓名',
                'phone': '电话',
                'email': '邮箱',
                'flight-number': '航班号',
                'pickup-datetime': '接机时间',
                'pickup-address': '接机地址',
                'dropoff-address': '送机地址',
                'car-type': '车型',
                'vehicle-type': '车型',
                'luggage-number': '行李数量',
                'passenger-number': '乘客人数',
                'language': '语言',
                'category': '类别',
                'subcategory': '子类别',
                'driving-region': '驾驶区域',
                'driver': '司机数量',
                'remark': '备注'
            };
            
            // 添加订单类型字段
                formattedData += `订单类型: ${orderTypeDisplay}\n`;
            
            // 添加类别和子类别信息
                const categoryText = dataToFormat.category === 'airport' ? 'airport' : 
                                     dataToFormat.category === 'charter' ? 'charter' : 
                                     dataToFormat.category || '';
                                     
                const subcategoryText = dataToFormat.subcategory === 'pickup' ? 'pickup' : 
                                       dataToFormat.subcategory === 'dropoff' ? 'dropoff' : 
                                       dataToFormat.subcategory === 'charter' ? 'charter' : 
                                       dataToFormat.subcategory || '';
                
                formattedData += `类别: ${categoryText}\n`;
                formattedData += `子类别: ${subcategoryText}\n`;
            
            // 格式化数据
            for (const [key, value] of Object.entries(dataToFormat)) {
                // 跳过vehicle-type如果car-type已经存在且相同
                if (key === 'vehicle-type' && dataToFormat['car-type'] === value) {
                    continue;
                }
                
                // 跳过已经处理的订单类型相关字段
                if (key === 'order-type' || key === 'category' || key === 'subcategory') {
                    continue;
                }
                
                if (value && fieldMapping[key]) {
                    let displayValue = value;
                    
                    if (key === 'driving-region') {
                            displayValue = orderProcessing.getDrivingRegionLabel(value);
                    }
                    
                    formattedData += `${fieldMapping[key]}: ${displayValue}\n`;
                }
            }
            
                this.copyToClipboard(formattedData);
            }
        };

        // 7. 设备工具
        const deviceUtils = {
            // 防抖函数
            debounce: function(func, wait) {
                let timeout;
                return function(...args) {
                    const context = this;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(context, args), wait);
                };
            },
            
            // 应用设备特定样式
            applyDeviceSpecificStyles: function() {
            const isMobile = window.matchMedia('(max-width: 767px)').matches;
            
            // 应用移动端特定样式
            if (isMobile) {
                // 调整按钮大小和触摸目标
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '44px';
                });
                
                // 调整表单元素大小
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '16px';
                    input.style.padding = '0.625rem';
                });
                
                // 调整多订单容器
                    if (dom.elements.ordersList) {
                        dom.elements.ordersList.style.gridTemplateColumns = '1fr';
                        dom.elements.ordersList.style.gap = '0.75rem';
                        dom.elements.ordersList.style.padding = '0.75rem';
                }
                
                // 添加移动端类到订单项
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.add('order-item-mobile');
                });
            } else {
                // 恢复桌面端样式
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '';
                });
                
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '';
                    input.style.padding = '';
                });
                
                    if (dom.elements.ordersList) {
                        dom.elements.ordersList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(280px, 1fr))';
                        dom.elements.ordersList.style.gap = '1rem';
                        dom.elements.ordersList.style.padding = '1rem';
                }
                
                // 移除移动端类
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.remove('order-item-mobile');
                });
            }
        }
        };

        // 8. 事件处理
        const eventHandlers = {
            // 处理转换按钮点击
            handleConvertBtnClick: async function() {
                const rawOrderText = dom.elements.rawOrderTextarea.value.trim();
                // **新增预校验**
                const dateTimeRegex = /(\d{1,2}[\\/\\-\\.月]\d{1,2}[\\/\\-\\.日]?)|(\d{1,2}[:.：]\d{1,2})/; // 简单日期或时间正则
                if (!rawOrderText) {
                    dom.showNotification(i18n.getString('please-enter-data'), 'warning');
                    return;
                }
                if (!dateTimeRegex.test(rawOrderText)) {
                     dom.showNotification('输入内容似乎缺少日期或时间信息，请检查。', 'warning');
                     return; // 如果没有日期时间，则不继续处理
                 }
                 // **预校验结束**

                // 禁用按钮，防止重复点击
                dom.elements.convertBtn.disabled = true;
                dom.elements.convertBtn.style.opacity = '0.7';
                dom.elements.convertBtn.style.cursor = 'wait';

                await orderProcessing.convertOrder();

                 // 恢复按钮状态
                 dom.elements.convertBtn.disabled = false;
                 dom.elements.convertBtn.style.opacity = '1';
                 dom.elements.convertBtn.style.cursor = 'pointer';
            },
            
            // 处理重置按钮点击
            handleResetBtnClick: function() {
                orderProcessing.resetForm();
            },
            
            // 处理编辑按钮点击
            handleEditBtnClick: function() {
                orderProcessing.enableFormEditing();
                dom.showNotification(i18n.getString('edit-enabled'));
            },
            
            // 处理保存按钮点击
            handleSaveBtnClick: function() {
                orderProcessing.disableFormEditing();
                dom.showNotification(i18n.getString('changes-saved'));
            },
            
            // 处理复制按钮点击
            handleCopyOutputBtnClick: function() {
                clipboard.copyFormData();
            },
            
            // 处理语言选择器变更
            handleLanguageSelectorChange: function(event) {
                const selectedLang = event.target.value;
                i18n.updateUI(selectedLang);
            },
            
            // 处理会话存储中的数据
            handleSessionStorageData: async function() {
                const orderData = sessionStorage.getItem('orderData');
                if (orderData) {
                    dom.elements.rawOrderTextarea.value = orderData;
                    // 清除sessionStorage中的数据
                    sessionStorage.removeItem('orderData');
                    // 自动处理订单
                    try {
                        await orderProcessing.convertOrder();
                } catch (error) {
                        console.error('自动处理订单失败:', error);
                        dom.showNotification('自动处理订单失败: ' + error.message);
                    }
                }
            },
            
            // 处理窗口大小变化
            handleWindowResize: deviceUtils.debounce(function() {
                deviceUtils.applyDeviceSpecificStyles();
            }, 100),

            // **新增：处理取消编辑按钮点击**
            handleCancelEditBtnClick: function() {
                console.log("取消编辑");
                orderProcessing.disableFormEditing(); // 禁用编辑，切换按钮
                // 如果是从多订单视图进入编辑的，则返回多订单视图
                if (appState.multiOrderViewActive) {
                    dom.hideElement(dom.elements.gridContainer);
                    dom.showElement(dom.elements.multiOrdersContainer);
                    appState.multiOrderViewActive = false; // 重置标记
                    // 可选：清空主表单，避免混淆
                    // orderProcessing.resetForm(); // 或者只清空主表单部分
                    } else {
                    // 如果是单订单编辑，可以考虑保留当前表单数据，仅禁用编辑
                    // 或者清空表单: orderProcessing.resetForm();
                }
                 dom.showNotification('编辑已取消', 'info');
            },

            // 新增：处理类别选择变化
            handleCategorySelectChange: function() {
                // 检查是否为机场类型，如果是且子类别为接机，则检查上车地址
                console.log('类别选择变化:', dom.elements.categorySelect.value);
                const updatePickupAddress = 
                    dom.elements.categorySelect.value === 'airport' && 
                    dom.elements.subcategorySelect.value === 'pickup' && 
                    (!dom.elements.pickupAddressInput.value || dom.elements.pickupAddressInput.value.trim() === '');
                
                if (updatePickupAddress) {
                    // 根据驾驶区域设置不同机场
                    const region = dom.elements.drivingRegionSelect.value || 'kl';
                    let airport = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                    if (region === 'sg') {
                        airport = 'Singapore Changi Airport';
                    } else if (region === 'kk') {
                        airport = 'Kota Kinabalu International Airport';
                    }
                    dom.elements.pickupAddressInput.value = airport;
                    console.log('类别变更为接机: 自动设置上车地址为机场:', airport);
                }
            },

            // 新增：处理子类别选择变化
            handleSubcategorySelectChange: function() {
                // 检查是否为接机子类别，如果是且类别为机场，则检查上车地址
                console.log('子类别选择变化:', dom.elements.subcategorySelect.value);
                const updatePickupAddress = 
                    dom.elements.categorySelect.value === 'airport' && 
                    dom.elements.subcategorySelect.value === 'pickup' && 
                    (!dom.elements.pickupAddressInput.value || dom.elements.pickupAddressInput.value.trim() === '');
                
                if (updatePickupAddress) {
                    // 根据驾驶区域设置不同机场
                    const region = dom.elements.drivingRegionSelect.value || 'kl';
                    let airport = 'Kuala Lumpur International Airport (KLIA/KLIA2)';
                    if (region === 'sg') {
                        airport = 'Singapore Changi Airport';
                    } else if (region === 'kk') {
                        airport = 'Kota Kinabalu International Airport';
                    }
                    dom.elements.pickupAddressInput.value = airport;
                    console.log('子类别变更为接机: 自动设置上车地址为机场:', airport);
                }
            }
        };

        // -------------------------
        //      初始化函数
        // -------------------------
        function initializeApp() {
            console.log('正在初始化应用程序...');
            
            // 1. 获取DOM元素
            dom.getElements();
            
            // 2. 设置初始语言
            const savedLang = localStorage.getItem('preferred-language') || 'zh';
            dom.elements.languageSelector.value = savedLang;
            i18n.updateUI(savedLang);
            
            // 3. 应用设备特定样式
            deviceUtils.applyDeviceSpecificStyles();
            
            // 4. 绑定事件处理器
            // 按钮点击事件
            dom.elements.convertBtn.addEventListener('click', eventHandlers.handleConvertBtnClick);
            dom.elements.resetBtn.addEventListener('click', eventHandlers.handleResetBtnClick);
            dom.elements.editBtn.addEventListener('click', eventHandlers.handleEditBtnClick);
            dom.elements.saveBtn.addEventListener('click', eventHandlers.handleSaveBtnClick);
            dom.elements.cancelEditBtn.addEventListener('click', eventHandlers.handleCancelEditBtnClick); // 新增绑定
            dom.elements.copyOutputBtn.addEventListener('click', eventHandlers.handleCopyOutputBtnClick);
            
            // 语言选择器事件
            dom.elements.languageSelector.addEventListener('change', eventHandlers.handleLanguageSelectorChange);
            
            // 类别和子类别选择事件
            dom.elements.categorySelect.addEventListener('change', eventHandlers.handleCategorySelectChange);
            dom.elements.subcategorySelect.addEventListener('change', eventHandlers.handleSubcategorySelectChange);
            
            // 窗口大小变化事件
            window.addEventListener('resize', eventHandlers.handleWindowResize);
            
            // 5. 处理会话存储中的数据
            eventHandlers.handleSessionStorageData();
            
            // 6. 禁用表单编辑（初始状态）
            orderProcessing.disableFormEditing();
            
            console.log('应用程序初始化完成');
        }

        // -------------------------
        //      应用程序入口点
        // -------------------------
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
    <!-- API加载器 -->
    <div id="api-loader">
        <div class="spinner-container">
            <div class="spinner"></div>
            <div class="spinner-text">正在处理，请稍候...</div>
        </div>
    </div>
</body>
</html>